# 一文读懂Harbor以及部署实践攻略

## 目录

- [1 概述](#1-概述)
  - [1.1 什么是Harbor](#11-什么是Harbor)
  - [1.2 Harbor的特性](#12-Harbor的特性)
  - [1.3 Harbor的架构](#13-Harbor的架构)
  - [1.4 Harbor的组件介绍](#14-Harbor的组件介绍)
    - [1.4.1 数据访问层（Data Access Layer ）](#141-数据访问层Data-Access-Layer-)
    - [1.4.2 基础服务（Fundamental Services ）](#142-基础服务Fundamental-Services-)
    - [1.4.3 消费者（Consumers ）](#143-消费者Consumers-)
- [2 Harbor部署](#2-Harbor部署)
  - [2.1 下载Harbor安装包](#21-下载Harbor安装包)
  - [2.2 通过HTTP协议提供服务](#22-通过HTTP协议提供服务)
    - [2.2.1 部署harbor](#221-部署harbor)
  - [2.3 使用systemd实现harbor开机启动](#23-使用systemd实现harbor开机启动)
    - [2.3.1 推送第一个镜像到Harbor](#231-推送第一个镜像到Harbor)
  - [2.4 通过HTTPS协议提供服务](#24-通过HTTPS协议提供服务)
  - [2.5 Harbor自签名证书方式](#25-Harbor自签名证书方式)
    - [2.5.1 切换到证书目录](#251-切换到证书目录)
    - [2.5.2 生成CA根证书](#252-生成CA根证书)
      - [******* 生成 CA 证书私钥](#2521-生成-CA-证书私钥)
      - [******* 生成 CA 证书](#2522-生成-CA-证书)
    - [2.5.3 生成Harbor服务器证书](#253-生成Harbor服务器证书)
      - [******* 生成私钥](#2531-生成私钥)
      - [******* 生成证书签名请求](#2532-生成证书签名请求)
      - [******* 生成 x509 v3 扩展文件。](#2533-生成-x509-v3-扩展文件)
      - [******* 使用v3.ext文件为你的 Harbor 主机生成证书。](#2534-使用v3ext文件为你的-Harbor-主机生成证书)
    - [2.5.4 签发Harbor 证书](#254-签发Harbor-证书)
    - [2.5.5 签发Docker 证书](#255-签发Docker-证书)
      - [******* 拷贝证书到docker](#2551-拷贝证书到docker)
      - [******* 重启 Docker Engine](#2552-重启-Docker-Engine)
      - [******* 添加hosts主机解析](#2553-添加hosts主机解析)
    - [2.5.6 验证 HTTPS 连接](#256-验证-HTTPS-连接)
      - [******* 浏览器访问harbor页面](#2561-浏览器访问harbor页面)
      - [******* docker客户端访问harbor](#2562-docker客户端访问harbor)
    - [2.5.7 尝试推送一个镜像](#257-尝试推送一个镜像)
  - [2.6 Harbor配置HTTPS异常问题](#26-Harbor配置HTTPS异常问题)
  - [2.7 SSL相关知识点](#27-SSL相关知识点)
    - [2.7.1 SSL证书](#271-SSL证书)
    - [2.7.2 X.509](#272-X509)
    - [2.7.3 自签名证书](#273-自签名证书)
    - [2.7.4 OpenSSL生产证书流程](#274-OpenSSL生产证书流程)
- [3 参考资料](#3-参考资料)

> ❤️ 摘要：感谢关注，原文来自：[https://www.cnblogs.com/StevenZengStudy/p/18402599](https://www.cnblogs.com/StevenZengStudy/p/18402599 "https://www.cnblogs.com/StevenZengStudy/p/18402599")

# 1 概述

## 1.1 什么是Harbor

Harbor 是一个企业级的云原生容器镜像仓库，由 VMware 主导开发并贡献给 Cloud Native Computing Foundation (CNCF)。它通过为 Docker 镜像提供安全、高效的管理能力，帮助企业简化容器应用程序的交付流程。相比于传统的 Docker Registry，Harbor 提供了更多的企业级特性，如容器镜像仓库之间的镜像复制、用户管理、访问控制、漏洞扫描和镜像签名等功能。

## 1.2 Harbor的特性

Harbor 的主要作用是为容器化应用程序提供集中式的镜像存储管理。它允许企业通过集中的仓库存储、分发和保护容器镜像，确保开发、测试和生产环境中使用的镜像都符合安全和合规要求。

Harbor有以下特性：

- **基于云原生场景：** Harbor 支持容器镜像和 Helm  Chart，可用作容器Runtime和编排平台等云原生环境的镜像仓库。
- **镜像管理**: Harbor 作为一个企业级的镜像仓库，支持 Docker 和 OCI 格式镜像的存储和管理。
- **细粒度的访问控制**: 通过基于角色的访问控制 (RBAC)，Harbor 能够确保不同用户在仓库中的操作权限得到精确控制。
- **镜像复制**: 支持跨多个 Harbor 实例进行镜像复制，帮助实现多数据中心或混合云环境下的高效镜像分发。
- **漏洞扫描**: 集成了 Clair 或 Trivy 等安全工具，Harbor 可以自动扫描镜像中的安全漏洞，确保部署的镜像安全。
- **镜像签名和内容信任**: 通过 Notary 集成，Harbor 支持镜像的签名和验证，确保镜像的完整性和可信度。
- **日志与审计**: 提供详细的操作日志和审计功能，帮助企业了解镜像的使用和管理情况。
- **多租户支持**: Harbor 支持项目隔离，帮助企业实现多租户环境下的镜像管理。
- **LDAP/AD 支持**：Harbor 与现有的企业 LDAP/AD 集成以进行用户身份验证和管理，并支持将 LDAP 组导入 Harbor，然后可以授予特定项目的权限。
- **镜像删除和垃圾收集**：系统管理员可以运行垃圾回收作业，以便可以删除**镜像**（悬挂的manifests 和未引用的blobs），并且可以定期释放这些空间。
- **审核**：对存储库的所有操作都通过日志进行跟踪。
- **RESTful API**：提供 RESTful API 以方便管理操作，并且易于使用以与外部系统集成。嵌入式 Swagger UI 可用于探索和测试 API。

## 1.3 Harbor的架构

Harbor 的架构设计遵循微服务原则，由多个松耦合的组件组成，每个组件负责不同的功能模块。在V2.0版本，已经完全符合 OCI 标准。

下图是 Harbor 的整体架构。

![](image/harbor_architecture_5EkeZMmD1p.png)

## 1.4 Harbor的组件介绍

如上图所示，Harbor 由放置在 3 层中的以下组件组成：

### 1.4.1 数据访问层（Data Access Layer ）

数据访问层主要负责存储和管理容器镜像以及相关的元数据，是整个 Harbor 系统的基础。它包括以下几个核心组件：

- **k-v 存储：** 由 Redis 组成，提供数据缓存功能，并支持临时持久化 Job 服务的 Job 元数据。
- **Registry（镜像仓库）**: 基于 Docker Registry 扩展的核心组件，负责存储容器镜像及其标签（tags）。它支持 Docker 和 OCI 格式的镜像，并允许通过 HTTP API 进行镜像的上传、下载和管理。
- **Database（数据库）**: Harbor 使用 PostgreSQL 数据库存储元数据，包括用户信息、项目、镜像标签、访问控制策略等。数据库是 Harbor 中各个功能模块元数据管理的核心。
- **Object Storage**: Harbor 也支持将镜像存储在对象存储中，支持的存储后端包括文件系统、S3、Ceph 等。对象存储用于存储实际的镜像层（layers）和其他大文件数据。

### 1.4.2 基础服务（Fundamental Services ）

基础服务，即是核心层，负责处理业务逻辑，是系统的心脏。这个层次负责管理用户、权限控制、镜像的生命周期、任务调度等操作。

- **Proxy：** 由 Nginx Server 组成的反向代理，提供 API 路由能力。Harbor 的组件，如核心、注册中心、Web 门户和 Token 服务等，都位于这个反向代理的后面。代理将来自浏览器和 Docker 客户端的请求转发到各种后端服务。

**核心：** Harbor 的核心服务，主要提供以下功能：

- **API Service**: 该组件是 Harbor 的核心服务，处理所有 API 请求。它负责用户身份验证、项目和镜像的管理，以及与其他微服务的交互。它还管理系统中的访问控制（RBAC）以及任务调度。
- **Job Service**: 负责异步任务的处理，比如镜像的复制、漏洞扫描等。它通过消息队列来调度任务，并监控任务的执行状态。
- **Notary**: 该组件用于提供镜像的签名与验证服务，确保镜像的来源可信。Notary 通过实现 Docker Content Trust (DCT) 来保护镜像的完整性。
- **Clair/Trivy**: 这是 Harbor 的漏洞扫描服务，负责对上传的镜像进行安全漏洞扫描。通过定期扫描镜像中的已知漏洞（CVE），帮助管理员发现并修复潜在的安全问题。
- \*\*Replication Controller \*\*（**复制控制器**）：管理复制策略和第三方容器仓库的适配器，触发和监控并发复制进程。实现了许多第三方容器仓库适配器，包括不限于docker registry、Docker Hub、Huawei SWR、阿里 ACR等。
- **webhook （通知管理器）**：在 Harbor 中配置的一种机制，以便可以将 Harbor 中的工件状态更改事件触发 Harbor 中配置的 Webhook 端点。而第三方应用可以通过侦听相关的 webhook 事件来触发一些后续操作。现在，支持两种方式：
  - HTTP Post 请求
  - Slack channel
- **Log collector（日志收集器）：** 日志收集器，负责将其他模块的日志收集到一个地方。

### 1.4.3 消费者（Consumers ）

- **外部客户端**：作为标准的云原生容器仓库，自然会支持相关客户端，如 docker CLI、notary 客户端、OCI 兼容客户端（如 Oras）和 Helm。除了这些客户端。
- **Web 门户：** 一个图形用户界面，可帮助用户管理 Registry 上的映像。

# 2 Harbor部署

> 系统环境：Ubuntu 22.04
> Docker  ： 27.1.2
> Docker-compose:  1.29.2
> Harbor  ： 2.10.3

## 2.1 下载Harbor安装包

安装包下载地址： [https://github.com/goharbor/harbor/tags](https://github.com/goharbor/harbor/tags "https://github.com/goharbor/harbor/tags")

![](image/image_9iv32nRgux.png)

选择offline版本安装包

![](image/image_wc10HvmMqO.png)

上传并解压harbor

```bash 
# 创建安装目录
mkdir /harbor
cd /harbor

# 上传并解压 
tar zxvf harbor-offline-installer-v2.10.3.tgz
harbor/harbor.v2.10.3.tar.gz
harbor/prepare
harbor/LICENSE
harbor/install.sh
harbor/common.sh
harbor/harbor.yml.tmpl

```


将镜像导入docker

```bash 
docker load -i harbor.v2.10.3.tar.gz

```


## 2.2 通过HTTP协议提供服务

> ⚠️  注意： http协议是不安全的，只能在开发或测试等内部环境使用。

### 2.2.1 部署harbor

复制配置模板保存为新的配置

```bash 
cp harbor.yml.tmpl harbor.yml
```


修改harbor.yml配置文件

```bash 

# 修改配置
vim harbor.yml
# 修改成主机ip
5 hostname: *************
6
7 # http related config
8 http:
9   # port for http, default is 80. If https enabled, this port will redirect to https port
  # 修改http端口
10   port: 80
11 # 注释https
12 # https related config
13 #https:
14   # https port for harbor, default is 443
15   #  port: 443
16   # The path of cert and key files for nginx
17   #certificate: /your/certificate/path
18   #private_key: /your/private/key/path
19   # enable strong ssl ciphers (default: false)
20   # strong_ssl_ciphers: false
# 修改自定义密码
37 harbor_admin_password: admin12345


```


预配置和部署

```bash 
./prepare && ./install.sh

```


修改配置文件

```bash 
# 复制配置模板
cp harbor.yml.tmpl harbor.yml
# 修改配置
vim harbor.yml
# 修改成主机ip
  5 hostname: *************
  6
  7 # http related config
  8 http:
  9   # port for http, default is 80. If https enabled, this port will redirect to https port
    # 修改http端口
 10   port: 80
 11 # 注释https
 12 # https related config
 13 #https:
 14   # https port for harbor, default is 443
 15   #  port: 443
 16   # The path of cert and key files for nginx
 17   #certificate: /your/certificate/path
 18   #private_key: /your/private/key/path
 19   # enable strong ssl ciphers (default: false)
 20   # strong_ssl_ciphers: false
 21
 22 # # Uncomment following will enable tls communication between all harbor components
 23 # internal_tls:
 24 #   # set enabled to true means internal tls is enabled
 25 #   enabled: true
 26 #   # put your cert and key files on dir
 27 #   dir: /etc/harbor/tls/internal
 28
 29
 30 # Uncomment external_url if you want to enable external proxy
 31 # And when it enabled the hostname will no longer used
 32 # external_url: https://reg.mydomain.com:8433
 33
 34 # The initial password of Harbor admin
 35 # It only works in first time to install harbor
 36 # Remember Change the admin password from UI after launching Harbor.
 # 修改自定义密码
 37 harbor_admin_password: Steven12345


```


预配置和部署

```bash 
./prepare && ./install.sh
```


查看harbor状态

```bash 
# 查看harbor状态
root@harbor:/harbor/# docker-compose ps
      Name                     Command                  State                      Ports
--------------------------------------------------------------------------------------------------------
harbor-core         /harbor/entrypoint.sh            Up (healthy)
harbor-db           /docker-entrypoint.sh 13 14      Up (healthy)
harbor-jobservice   /harbor/entrypoint.sh            Up (healthy)
harbor-log          /bin/sh -c /usr/local/bin/ ...   Up (healthy)   127.0.0.1:1514->10514/tcp
harbor-portal       nginx -g daemon off;             Up (healthy)
nginx               nginx -g daemon off;             Up (healthy)   0.0.0.0:80->8080/tcp,:::80->8080/tcp
redis               redis-server /etc/redis.conf     Up (healthy)
registry            /home/<USER>/entrypoint.sh       Up (healthy)
registryctl         /home/<USER>/start.sh            Up (healthy)


```


后续操作，可以通过docker-compose管理harbor。

- 关闭harbor

```bash 
root@harbor:~/harbor/harbor# docker-compose stop
Stopping harbor-jobservice ... done
Stopping nginx             ... done
Stopping harbor-core       ... done
Stopping registryctl       ... done
Stopping harbor-db         ... done
Stopping redis             ... done
Stopping registry          ... done
Stopping harbor-portal     ... done
Stopping harbor-log        ... done

```


- 启动harbor

```bash 
root@harbor:~/harbor/harbor# docker-compose start
Starting log         ... done
Starting registry    ... done
Starting registryctl ... done
Starting postgresql  ... done
Starting portal      ... done
Starting redis       ... done
Starting core        ... done
Starting jobservice  ... done
Starting proxy       ... done

```


- 创建harbor

```bash 
 docker-compose up -d
```


- 删除harbor

```bash 
docker-compose down
```


## 2.3 使用systemd实现harbor开机启动

编辑/etc/systemd/system/harbor.service

```bash 
[Unit]
Description=Harbor
After=docker.service systemd-networkd.service systemd-resolved.service
Requires=docker.service
Documentation=http://github.com/vmware/harbor

[Service]
Type=simple
Restart=on-failure
RestartSec=5
ExecStart=/usr/bin/docker-compose -f /root/harbor/harbor/docker-compose.yml up
ExecStop=/usr/bin/docker-compose -f /root/harbor/harbor/docker-compose.yml down

[Install]
WantedBy=multi-user.target
```


启动命令

```bash 
systemctl daemon-reload
systemctl enable harbor --now
```


### 2.3.1 推送第一个镜像到Harbor

尝试登录harbor页面

![](image/image_hXwJ_XERSn.png)

❔ 说明：

- 账号：admin
- 密码： 配置文件写的

docker添加Harbor仓库

```bash 
vim /etc/docker/daemon.json

{
  "registry-mirrors": [
          "https://6qxc6b6n.mirror.aliyuncs.com",
          "https://22ba4b5fbc4c4261bda590dcf3b3d1a4.mirror.swr.myhuaweicloud.com",
        "https://atomhub.openatom.cn/"
  ],
  # 添加Harbor仓库地址
  "insecure-registries": ["http://*************"]
}

```


重启docker

```bash 
systemctl daemon-reload
systemctl restart docker

```


查看dcoker是否配置成功

```bash 
docker info
...
 Insecure Registries:
  *************
  127.0.0.0/8
 Registry Mirrors:
  https://6qxc6b6n.mirror.aliyuncs.com/
  https://22ba4b5fbc4c4261bda590dcf3b3d1a4.mirror.swr.myhuaweicloud.com/
  https://atomhub.openatom.cn/
...
```


docker客户端登录harbor

```bash 
root@jenkins:~# docker login *************
Username: admin
Password:
WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
Configure a credential helper to remove this warning. See
https://docs.docker.com/engine/reference/commandline/login/#credential-stores

Login Succeeded

```


尝试推送一个镜像（busybox:1.29-2）到harbor，修改镜像的tag

```bash 
root@docker1:~# docker tag registry.cn-hangzhou.aliyuncs.com/hcie/busybox:1.29-2 *************/library/busybox:1.29-2


```


执行以下命令推送镜像到harbor的library项目

```bash 
root@docker1:~# docker push *************/library/busybox:1.29-2
The push refers to repository [*************/library/busybox]
23bc2b70b201: Pushed
1.29-2: digest: sha256:c26091c675b78a3ab71f78839315225279e6aa11660079136bf524d6285488ec size: 526
```


在harbor页面查看镜像信息

- 路口： 项目→library项目

![](image/image_sjzv_8t0TK.png)

> ❔  说明： 镜像可以推送成功

## 2.4 通过HTTPS协议提供服务

> ❔ 说明：  生产环境建议配置https协议，配置 HTTPS，你必须创建 SSL 证书。你可以使用由受信任的第三方 CA 签名的证书，也可以使用自签名证书。下面演示是自签名证书。

## 2.5 Harbor自签名证书方式

### 2.5.1 切换到证书目录

```bash 
mkdir /harbor/pki 
cd    /harbor/pki
```


### 2.5.2 生成CA根证书

要生成 CA 证书，请运行以下命令。

#### ******* 生成 CA 证书私钥

```text 
openssl genrsa -out ca.key 4096 

```


#### ******* 生成 CA 证书

如果你使用域名FQDN比如`yourdomain.com`方式来连接harbor容器仓库，则你必须使用`yourdomain.com`来作为CN(Common Name)。或者你使用IP地址来访问harbor的话，CN可以指定为任何值。

执行以下命令，生成自签名CA证书，后面的CN都指定为`harbor.zx`

```text 
openssl req -x509 -new -nodes -sha512 -days 3650 \
 -subj "/C=CN/ST=GuangDong/L=Foshan/O=example/OU=Personal/CN=harbor.zx" \
 -key ca.key \
 -out ca.crt
 


```


> ❔ 参数说明：

| 参数      | 说明                                |
| ------- | --------------------------------- |
| req     | 产生证书签发申请命令                        |
| -x509   | 签发X.509格式证书命令。X.509是最通用的一种签名证书格式。 |
| -new    | 生成证书请求                            |
| -nodes  | 表示私钥不加密                           |
| -sha512 | 使用SHA512算法                        |
| -key    | 指定私钥文件                            |
| -out    | 输出保存为ca证书                         |
| -subj   | 指定用户信息                            |
| -days   | 有效期， 这里设置成10年                     |

> ❔ 说明：CN(Common Name)一般来讲就是填写你将要申请SSL证书的域名 (domain)或子域名(sub domain)。

- 例1：如果你打算为“chinassl.net”申请SSL证 书，那这个公用名(Common Name)就要填写“chinassl.net”，而不能填写 “[www.chinassl.net”，因为在申请SSL证书时发证机构认为“www.yourdomain.com”和](http://www.chinassl.net”，因为在申请SSL证书时发证机构认为“www.yourdomain.com”和) “yourdomain.com”是不同的两个域名；
- 例2：如果你要为bill.chinassl.net申请SSL证书，那么这里公用名(Common Name)就 要填写“bill.chinassl.net”而不能填写“chinassl.net”或“[www.chinassl.net”](http://www.chinassl.net”)

### 2.5.3 生成Harbor服务器证书

证书通常包含一个`.crt`文件和一个`.key`文件，例如，`yourdomain.com.crt`和`yourdomain.com.key`.

#### ******* 生成私钥

执行以下命令生成服务器证书私钥

```text 
openssl genrsa -out harbor.zx.key 4096

```


#### ******* 生成证书签名请求

执行以下命令生成服务器证书签名请求

```text 
openssl req -sha512 -new \
    -subj "/C=CN/ST=GuangDong/L=Foshan/O=example/OU=Personal/CN=harbor.zx" \
    -key harbor.zx.key \
    -out harbor.zx.csr

```


#### ******* 生成 x509 v3 扩展文件。

无论你是使用 FQDN 还是 IP 地址来连接到你的 Harbor 主机，你都必须创建此文件，以便你可以生成符合Subject Alternative Name (SAN) 和 x509 v3 的证书扩展要求。

以下是域名访问方式，`[alt_names]`下可以添加多条DNS解析记录

```bash 
cat > v3.ext <<-EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

# 以下是自定义域名记录
[alt_names]
DNS.1=registry.harbor.zx
DNS.2=harbor.zx
DNS.3=harbor
EOF
```


以下是IP访问方式，

- `subjectAltName` 字段替换成harbor的自定义ip

```text 
cat > v3.ext <<-EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = IP:*************
EOF


```


- 或者`[alt_names]`下可以添加多条DNS解析记录

```text 
cat > v3.ext <<-EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth

[alt_names]
IP.1 = **********
IP.2 = *************
EOF

```


#### ******* 使用`v3.ext`文件为你的 Harbor 主机生成证书。

将CRS 和 CRT 文件名中的`yourdomain.com` 替换为 Harbor 自定义域名。

```text 
openssl x509 -req -sha512 -days 3650 \
   -extfile v3.ext \
   -CA ca.crt -CAkey ca.key -CAcreateserial \
   -in harbor.zx.csr \
   -out harbor.zx.crt

```


> ❔ 参数说明：

| 参数              | 说明                          |
| --------------- | --------------------------- |
| x509            | 签发X.509格式证书命令。              |
| -req            | 表示证书输入请求。                   |
| -days           | 表示有效天数                      |
| -extensions     | 表示按OpenSSL配置文件v3\_req项添加扩展。 |
| -CA             | 表示CA证书,这里为ca.crt            |
| -CAkey          | 表示CA证书密钥,这里为ca.key          |
| -CAcreateserial | 表示创建CA证书序列号                 |
| -extfile        | 指定扩展文件                      |

### 2.5.4 签发Harbor 证书

复制服务器证书和密钥到 Harbor 主机上的 certficates 文件夹中。

```text 
cp harbor.zx.crt /data/cert/
cp harbor.zx.key /data/cert/

```


修改harbor.yaml

```bash 
# 切换目录
cd /harbor

#修改harbor配置
vim harbor.yaml

# 修改访问地址，跟上面指定的CN一致
hostname: harbor.zx

# 注释http相关配置
# http related config
#http:
  # port for http, default is 80. If https enabled, this port will redirect to https port
  #port: 80
# 取消https相关配置
# https related config
https:
  # https port for harbor, default is 443
  # 默认是443端口，避免其他应用占用该端口
  port: 443
  
  # The path of cert and key files for nginx
  # 指定服务器证书
  certificate: /data/cert/harbor.zx.crt
  # 指定服务器证书私钥
  private_key: /data/cert/harbor.zx.key


```


重新生产harbor的docker-compose.yml

```bash 
./prepare
```


关闭harbor服务

```bash 
docker-compose down

```


启动harbor服务

```bash 
docker-compose up -d
```


### 2.5.5 签发Docker 证书

Docker 守护程序将`.crt`文件解释为 CA 证书，将`.cert`文件解释为客户端证书。

```text 
openssl x509 -inform PEM -in harbor.zx.crt -out harbor.zx.cert

```


#### ******* 拷贝证书到docker

将服务器证书、密钥和 CA 文件复制到 Harbor 主机上的 Docker 证书文件夹中。

如果是通过IP访问

```bash 
# 如果如下目录不存在，先创建
mkdir -p /etc/docker/certs.d/harbor_IP

# 例如：
mkdir -p /etc/docker/certs.d/*************

# 如果端口为443，则不需要指定。如果为自定义端口，请指定端口
mkdir -p  /etc/docker/certs.d/*************:8443

# 复制证书
cp *************.cert /etc/docker/certs.d/*************/
cp *************.key /etc/docker/certs.d/*************/
cp ca.crt            /etc/docker/certs.d/*************/

```


如果是通过域名访问

```text 
# 创建证书目录
mkdir -p /etc/docker/certs.d/harbor.zx

# 如果端口为443，则不需要指定。如果为自定义端口，请指定端口
mkdir -p /etc/docker/certs.d/harbor.zx:8443

# 复制证书
cp harbor.zx.cert    /etc/docker/certs.d/harbor.zx/
cp harbor.zx.key     /etc/docker/certs.d/harbor.zx/
cp ca.crt            /etc/docker/certs.d/harbor.zx/


```


证书目录结构如下

```bash 
# 以下示例说明了使用自定义证书的配置。
/etc/docker/certs.d/
    └── harbor.zx:port
       ├── harbor.zx.cert  <-- Server certificate signed by CA
       ├── harbor.zx.key   <-- Server key signed by CA
       └── ca.crt               <-- Certificate authority that signed the registry certificate
```


配置docker的daemon.json

```bash 
vim /etc/docker/daemon.json
{
  "registry-mirrors": [
          "https://6qxc6b6n.mirror.aliyuncs.com",
          "https://22ba4b5fbc4c4261bda590dcf3b3d1a4.mirror.swr.myhuaweicloud.com",
        "https://atomhub.openatom.cn/",
        "https://harbor.zx"
  ]
}

```


#### ******* 重启 Docker Engine

```text 
systemctl daemon-reload
systemctl restart docker

```


#### ******* 添加hosts主机解析

```bash 
vim /etc/hosts
************* harbor.zx

```


你可能还需要在操作系统级别信任证书。有关更多信息，请参阅 [Harbor 安装故障排除。](https://goharbor.io/docs/2.2.0/install-config/troubleshoot-installation/#https "Harbor 安装故障排除。")

### 2.5.6 验证 HTTPS 连接

为 Harbor 设置 HTTPS 后，你可以通过执行以下步骤来验证 HTTPS 连接。

#### ******* 浏览器访问harbor页面

打开浏览器并输入[https://yourdomain.com](https://yourdomain.com/ "https://yourdomain.com")。它应该显示 Harbor 界面。

> ❔ 说明： 某些浏览器可能会显示一条警告，指出证书颁发机构 (CA) 未知。当使用不是来自受信任的第三方 CA 的自签名 CA 时，会发生这种情况。你可以将 CA 导入浏览器以删除警告。

![](image/image_XIlNrsp7P-.png)

#### ******* docker客户端访问harbor

Docker 客户端登录到 Harbor，执行以下命令

```text 
docker login yourdomain.com

```


测试登录

```bash 
root@docker1:~# docker login harbor.zx
Username: admin
Password:
WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
Configure a credential helper to remove this warning. See
https://docs.docker.com/engine/reference/commandline/login/#credential-stores

Login Succeeded

```


### 2.5.7 尝试推送一个镜像

尝试推送一个镜像（stress:latest）到harbor，修改镜像的tag

```bash 
docker tag docker.io/polinux/stress:latest harbor.zx/library/stress:latest


```


执行以下命令推送镜像到harbor的library项目

```bash 
root@jenkins:/etc/docker# docker push harbor.zx/library/stress:latest
The push refers to repository [harbor.zx/library/stress]
3c9dd45820df: Pushed
6bc83681f1ba: Pushed
latest: digest: sha256:b6144f84f9c15dac80deb48d3a646b55c7043ab1d83ea0a697c09097aaad21aa size: 739
```


在harbor页面查看镜像信息

- 路口： 项目→library项目

![](image/image_HcXrpLukSi.png)

> ❔  说明： 镜像可以推送成功

## 2.6 Harbor配置HTTPS异常问题

Docker守护程序将`.crt`文件作为CA证书，并将`.cert`文件作为客户端证书。如果一个CA证书被意外赋予扩展名`.cert`而不是正确的`.crt`扩展名，则Docker守护程序会记录以下错误消息：

```text 
Missing key KEY_NAME for client certificate CERT_NAME. 
CA certificates should use the extension .crt.

```


如果Docker镜像仓库没有端口号，请不要将端口添加到目录名称中。

下面显示了默认443端口上镜像像库的配置，非443端口则需要在目录名称中添加端口号：

```text 
/etc/docker/certs.d/
   └── my-https.registry.example.com          <-- Hostname without port
      ├── client.cert
      ├── client.key
      └── ca.crt

```


## 2.7 SSL相关知识点

### 2.7.1 SSL证书

&#x20;   SSL（Secure Sockets Layer）证书是一种数字证书，用于加密客户端与服务器之间的通信，确保数据在传输过程中的机密性和完整性。随着时间推移，SSL 已经被其升级版本 TLS（Transport Layer Security）所取代，但为了方便起见，很多人依然使用 SSL 来泛指这类加密协议。

&#x20;   SSL网站不同于一般的Web站点，它使用的是“HTTPS”协议，而不是普通的“HTTP”协议。因此它的URL（统一资源定位器）格式为“[https://www.baidu.com](https://www.baidu.com "https://www.baidu.com")”。

### 2.7.2 X.509

&#x20;    X.509 是一种公共密钥证书的标准格式，定义了 SSL/TLS 证书的结构。SSL 证书通常采用 X.509 标准，该标准规定了如何描述证书的格式及其相关数据。X.509 证书中包含的信息主要包括：

- **主体信息（Subject）**: 包含证书持有者的身份信息，如域名、组织名等。
- **颁发者信息（Issuer）**: 证书的颁发机构（Certificate Authority, CA）的信息。
- **公钥（Public Key）**: 证书的持有者所使用的公钥，用于加密和验证签名。
- **签名（Signature）**: 由颁发者使用其私钥生成的签名，确保证书的真实性和完整性。

&#x20; X.509 证书通常涉及三个文件：`key`、`csr`、和 `crt`。

- **key** 是服务器的私钥文件，负责解密从客户端接收到的数据，并用于对发送给客户端的数据进行签名。
- **csr** 是证书签名请求文件，包含服务器的公钥和身份信息，用于提交给证书颁发机构（CA）以申请签名证书。
- **crt** 是 CA 签发的证书文件，或者是由开发者自签名的证书，包含证书持有人的身份信息、公钥以及签发者的签名。

&#x20; X.509 证书是一种链式信任体系，客户端会通过信任的根证书向下验证每一级证书的签名，直到最终确认服务器证书的合法性。

### 2.7.3 自签名证书

&#x20;   自签名证书是由自己生成和签名的 SSL 证书，而非由受信任的 CA 签发。自签名证书在某些场景中很有用，例如测试环境或内部系统中使用加密通信时，无需支付给第三方 CA 获取证书。

然而，由于自签名证书没有经过受信任的机构签发，浏览器和其他客户端通常会将其标记为不安全。要使用自签名证书，用户必须手动信任该证书。

### 2.7.4 OpenSSL生产证书流程

```mermaid 
sequenceDiagram
    participant Client
    participant Server
    participant CA

    Server->>Server: 生成私钥 (key)
    Server->>Server: 生成CSR文件 (csr)
    Server->>CA: 向CA服务器提交CSR
    CA-->>Server: 签发并向服务器返回CA证书 (crt)
    Server->>Server: 配置证书
    Client->>Server: 请求HTTPS连接
    Server-->>Client: 发送证书 (crt)
    Client->>Client: 验证证书
    Client-->>Server: 建立加密通信
```


❔ 说明：&#x20;

1. **服务器生成私钥和 CSR 文件** （第1-2步）
   - 服务器使用 OpenSSL 生成自己的私钥 (`myprivate.key`)。
   - 然后，服务器基于私钥生成一个证书签名请求（CSR，`myrequest.csr`），包含服务器的公钥和其他信息（如域名、组织等）。
2. **服务器向 CA 提交 CSR**（第3步）
   - 服务器将 CSR 文件发送给 CA 服务器，请求签发证书。
3. **CA 签发证书**（第4步）
   - CA 服务器根据收到的 CSR 生成 SSL 证书，并用 CA 的私钥对证书进行签名。
   - CA 服务器将签名后的证书（`mycertificate.crt`）返回给服务器。
4. **服务器配置 SSL 证书**（第5步）
   - 服务器将获得的 SSL 证书和生成的私钥配置到服务器的 Web 服务中（如 Nginx 或 Apache）。
5. **客户端请求 HTTPS 连接**（第6步）
   - 客户端通过浏览器或其他方式请求服务器上的 HTTPS 服务。
6. **服务器发送证书给客户端**（第7步）
   - 服务器响应客户端的请求，发送 SSL 证书给客户端。
7. **客户端验证证书**（第8-9步）
   - 客户端使用本地的受信 CA 列表（信任链）来验证服务器证书的合法性。如果证书是由受信任的 CA 颁发，客户端将信任该证书并建立加密连接。

# 3 参考资料

【1】 [https://goharbor.io/docs/2.11.0/install-config/configure-https/](https://goharbor.io/docs/2.11.0/install-config/configure-https/ "https://goharbor.io/docs/2.11.0/install-config/configure-https/")

【2】 [https://blog.csdn.net/u013066244/article/details/78725842](https://blog.csdn.net/u013066244/article/details/78725842 "https://blog.csdn.net/u013066244/article/details/78725842")
