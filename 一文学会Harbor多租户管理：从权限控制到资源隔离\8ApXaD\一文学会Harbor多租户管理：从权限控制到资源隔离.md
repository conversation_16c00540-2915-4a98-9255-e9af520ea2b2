# 一文学会Harbor多租户管理：从权限控制到资源隔离

## 目录

- [](#)
- [Harbor多租户概述](#Harbor多租户概述)
  - [Harbor 多租户隔离模型](#Harbor-多租户隔离模型)
  - [项目即租户：Harbor的隔离哲学](#项目即租户Harbor的隔离哲学)
    - [角色权限模型：精细化的访问控制](#角色权限模型精细化的访问控制)
- [实验案例](#实验案例)
  - [🗒️ 实验应用场景](#️-实验应用场景)
  - [👥 实验角色分配  ](#-实验角色分配--)
  - [🛠️ 实验步骤  ](#️-实验步骤--)
    - [创建项目](#创建项目)
    - [创建用户](#创建用户)
    - [配置用户授权](#配置用户授权)
    - [补充知识点：harbor-cli查询harbor的配置](#补充知识点harbor-cli查询harbor的配置)
    - [2. 开发团队隔离验证  ](#2-开发团队隔离验证--)
      - [场景：TeamA尝试访问TeamB的镜像  ](#场景TeamA尝试访问TeamB的镜像--)
    - [3. 运维团队只读审计验证  ](#3-运维团队只读审计验证--)
      - [场景：运维查看所有项目但无法修改  ](#场景运维查看所有项目但无法修改--)
    - [4. 安全团队漏洞扫描验证  ](#4-安全团队漏洞扫描验证--)
      - [场景：安全团队执行扫描并查看报告](#场景安全团队执行扫描并查看报告)
  - [💡 实验结论  ](#-实验结论--)
- [总结](#总结)
- [参考文档：](#参考文档)

#

![](https://goharbor.io/img/logos/harbor-horizontal-color.png)

📚  **博客主页：** \<font face="黑体" size=5.5>[**StevenZeng学堂**](https://blog.csdn.net/u013522701?spm=1010.2135.3001.5343 "StevenZeng学堂")\</font>

🎉  **博客专栏:** &#x20;

- [**一文读懂Kubernetes**](https://blog.csdn.net/u013522701/category_12778372.html?spm=1001.2014.3001.5482 "一文读懂Kubernetes")&#x20;
- [**一文读懂Harbor**](https://blog.csdn.net/u013522701/category_12778948.html?spm=1001.2014.3001.5482 "一文读懂Harbor")
- [**云原生安全实战指南**](https://blog.csdn.net/u013522701/category_12785632.html "云原生安全实战指南")&#x20;
- [**云原生存储实践指南**](https://blog.csdn.net/u013522701/category_12811050.html?spm=1001.2014.3001.5482 "云原生存储实践指南")

***

![](https://img-blog.csdnimg.cn/img_convert/c9fc944bbbaf0e567b4659df99169fed.gif)

***

> ❤️ 摘要： 云原生环境中，镜像仓库的多租户管理是确保资源隔离、权限控制和高效协作的重要组成部分。Harbor 作为一个开源的企业级镜像仓库，它提供了强大的多租户管理功能，包括用户管理、角色分配、项目隔离等。本文将带你快速掌握如何在 Harbor 中实现高效的多租户管理。

***

**💯 本文关联好文:**

- [《一文读懂Harbor以及部署实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142024722 "《一文读懂Harbor以及部署实践攻略》")
- 《[【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践](https://stevenzeng.blog.csdn.net/article/details/142470707 "【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践")》
- 《[一文掌握Harbor镜像同步公有云镜像仓库实践](https://stevenzeng.blog.csdn.net/article/details/142649705 "一文掌握Harbor镜像同步公有云镜像仓库实践")》

***

✍🏻\*\* 本文知识点：\*\*

- 多租户管理
- 项目
- 用户角色

***

# Harbor多租户概述

## Harbor 多租户隔离模型

Harbor 中的多租户隔离功能主要依赖于两个概念：**项目** 和 **角色权限管理**。

- **项目（Project）**：项目是 Harbor 中最基本的管理单元。每个项目可以视为一个命名空间，所有镜像、制品等数据都存储在项目中。项目是完全隔离的，项目之间的镜像不会互相影响。
- **角色权限管理**：Harbor 通过角色定义了不同用户对项目的访问和操作权限。角色包括：**项目管理员（Project Admin）**、**开发人员（Developer）**、**访客（Guest）** 和 **维护人员（Maintainer）**，每个角色有不同的权限级别。

## 项目即租户：Harbor的隔离哲学

Harbor通过**项目(Project)** 实现多租户隔离，每个项目本质上是独立的命名空间。这种设计就像给每个团队分配独立的保险柜：

- **完全隔离**：`teamA`项目的镜像对`teamB`不可见（除非显式授权）
- **独立配置**：每个项目可单独设置：
  - 访问权限（公开/私有）
  - 存储配额
  - 漏洞扫描策略
  - Webhook规则
- &#x20;**隔离级别对比**：
  - **公有项目**：所有用户可见（类似商场展示柜）
  - **私有项目**：仅成员可见（需钥匙才能开的保险柜）

### 角色权限模型：精细化的访问控制

Harbor 提供了多种用户类型和角色，以满足不同场景下的权限需求。Harbor的RBAC系统提供5+2种角色配置：

| **角色类型**​                | **描述**​                                                                             | **典型权限**​ | **适用场景**​     |
| ------------------------ | ----------------------------------------------------------------------------------- | --------- | ------------- |
| **系统管理员**​               | Harbor 系统管理员拥有最高权限，可以执行所有操作。                                                        | 所有权限      | 基础设施团队        |
| **Project Admin**​       | 创建新项目时，您将被赋予该项目的“ProjectAdmin”角色。除了读写权限外，“ProjectAdmin”还拥有一些管理权限，例如添加和移除成员、启动漏洞扫描等。 | 项目管理/成员管理 | 团队负责人         |
| **Developer**​           | 开发人员拥有项目的读写权限。                                                                      | 推送/拉取镜像   | 开发工程师         |
| **Maintainer**​          | 维护者的权限高于“开发人员”，包括扫描镜像、查看复制任务以及删除镜像和helm charts的权限。                                  | 镜像维护/扫描   | 运维工程师、安全运维工程师 |
| **Guest**​               | 访客拥有指定项目的只读权限。他们可以拉取镜像并重新标记镜像，但无法推送镜像。                                              | 只读权限      | 测试人员          |
| **Limited Guest**​       | 受限访客不具备项目的完全读取权限。他们可以拉取镜像，但无法推送，也无法查看日志或项目的其他成员。                                    | 受限只读      | 外包人员          |
| \*\*Anonymous\*\*\&#x20; | 当用户未登录时，该用户将被视为“匿名”用户。匿名用户无法访问私人项目，但对公共项目拥有只读权限。                                    | 公有项目只读    | 未登录用户\&#x20;  |

![角色权限矩阵](https://goharbor.io/docs/2.12.0/img/rbac.png "角色权限矩阵")

***

# 实验案例

> 实践案例基于Harbor的本地数据库的认证模式

## 🗒️ 实验应用场景

- **开发团队A** 和 **开发团队B** 共享一个 Harbor 实例，但需要独立的存储空间，确保两者的镜像不会互相影响。
- **运维团队** 可以拥有所有项目的只读权限，以便对所有镜像进行审计和安全检查。
- **安全团队** 可以访问所有项目的漏洞扫描报告。

## **👥 实验角色分配** &#x20;

| 团队        | 用户名           | 分配角色                 | 预期权限             |
| --------- | ------------- | -------------------- | ---------------- |
| 开发团队A     | \`dev-teamA\` | Developer (ProjectA) | 仅能读写\`projectA\` |
| 开发团队B     | \`dev-teamB\` | Developer (ProjectB) | 仅能读写\`projectB\` |
| 运维团队      | \`ops-user\`  | Guest (All Projects) | 所有项目只读           |
| 安全团队      | \`sec-user\`  | Maintainer (All)     | 可扫描镜像，并查询扫描报告    |
| Harbor管理员 | \`admin\`     | System Administrator | 全权限              |

## **🛠️ 实验步骤** &#x20;

> 这里使用web方式进行实验操作

***

### 创建项目

登录harbor后，点击项目，点击新建项目

![](image/image_cXHr-xERlK.png)

创建项目A，命名为projecta

![](image/image_-l5naHT-hD.png)

创建项目b，命名为projectb

![](image/image_0VxCMTIRJJ.png)

### 创建用户

点击系统管理-用户管理-新建用户

![](image/image_Dw6_hv9ten.png)

创建开发账号

![](image/image_SqgBefeSvS.png)

![](image/image_QjQuMIbgr9.png)

创建运维账号

![](image/image_etQ6WEPyuB.png)

创建安全审计账号

![](image/image_ii4BdePGoE.png)

### 配置用户授权

在Project A项目中给对应的用户添加对应的权限，比如dev-teamA是开发者角色

![](image/image_1Hq1TQsEfo.png)

ops-user是访客角色

![](image/image_DDbcAb53-3.png)

sec-user是维护者角色

![](image/image_3kBOv6-Q6T.png)

最终设置

![](image/image_UwI7-6a6zk.png)

同样对Project B项目操作，这里忽略步骤。

![](image/image_A2wfm9d5dW.png)

### 补充知识点：harbor-cli查询harbor的配置

> Harbor CLI —— 用于与 Harbor 交互的命令行界面。它是一款精简、用户友好的 WebUI 替代方案，可用作日常驱动程序或用于脚本编写和自动化。如果是经常操作harbor的同学，harbor-cli对你来说是一把利器。

下载Harbor CLi安装包，下载地址：[https://github.com/goharbor/harbor-cli/releases](https://github.com/goharbor/harbor-cli/releases "https://github.com/goharbor/harbor-cli/releases")

![](image/image_bWX6nebgsS.png)

当前环境是ubuntu 22，所以直接下载deb安装包

```bash 
wget https://github.com/goharbor/harbor-cli/releases/download/v0.0.7/harbor_0.0.7_linux_amd64.deb
```


安装harbor-cli工具

```bash 
dpkg -i harbor_0.0.7_linux_amd64.deb
```


检查harbor-cli版本

```markdown 
root@rke2 # harbor version
INFO[0000] System keyring not available, using file-based keyring
Version:      v0.0.7
Go version:
Git commit:   1fbe629fef17bfbcb44847f2d8a03139990b68b7
Built:
OS/Arch:      linux/amd64

```


登录harbor

```bash 
harbor login https://harbor.zx -u admin -p Harbor12345
```


输出

```markdown 
INFO[0000] System keyring not available, using file-based keyring
Login successful for admin at https://harbor.zx

```


查看harbor的项目列表

```bash 
harbor project list
```


输出

```markdown 
┌──────────────────────────────────────────────────────────────────────────────────────────────────┐
│  ID    Project Name              Access Level      Type              Repo Co…  Creation Time     │
│ ──────────────────────────────────────────────────────────────────────────────────────────────── │
│  1     library                   public            project           1         1 day ago         │
│  2     projecta                  private           project           0         36 minute ago     │
│  3     projectb                  private           project           0         35 minute ago     │
└──────────────────────────────────────────────────────────────────────────────────────────────────┘

```


查看用户列表

```bash 
harbor user list
```


输出

```markdown 
┌────────────────────────────────────────────────────────────────────────────────────────┐
│  ID    Name              Administrator     Email                     Registration Ti…  │
│ ────────────────────────────────────────────────────────────────────────────────────── │
│  3     dev-teamA         No                <EMAIL>        49 minute ago     │
│  4     dev-teamB         No                <EMAIL>        49 minute ago     │
│  5     ops-user          No                <EMAIL>         46 minute ago     │
│  6     sec-user          No                <EMAIL>         46 minute ago     │
└────────────────────────────────────────────────────────────────────────────────────────┘
```


### **2. 开发团队隔离验证** &#x20;

向ProjectA和ProjectB分别推送测试镜像，这里直接将harbor的组件镜像推送到项目中。

![](image/image_O5DXDEgeMM.png)

#### **场景：TeamA尝试访问TeamB的镜像** &#x20;

&#x20;在测试主机1做以下操作：

TeamA用户登录docker

```bash 
docker login -u dev-teamA harbor.zx
Password:


```


输出

```markdown 
WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
Configure a credential helper to remove this warning. See
https://docs.docker.com/engine/reference/commandline/login/#credential-stores

Login Succeeded
```


⚠️ **注意**：&#x20;

```docker 
Error response from daemon: Get "https://harbor.zx/v2/": tls: failed to verify certificate: x509: certificate signe
```


💖 如果主机登录出现证书验证失败，参考[《一文读懂Harbor以及部署实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142024722 "《一文读懂Harbor以及部署实践攻略》")的2.4.4.1 拷贝证书到docker

通过harbor web，TeamA无法看到projectB

![](image/image_7QzE-kHFZF.png)

通过admin获取TeamB的镜像，并尝试拉取TeamB的镜像

![](image/image_R2sXCb_8iT.png)

命令如下：

```bash 
docker pull harbor.zx/projectb/harbor-db@sha256:fcbb54e7d3241603743450ea60f62686f9cc9eba4c82ababd46f963b0ca399fa


```


输出

```markdown 
Error response from daemon: unauthorized: unauthorized to access repository: projectb/harbor-db, action: pull: unauthorized to access repository: projectb/harbor-db, action: pull
```


**验证点**： &#x20;

✅ TeamA用户无法看到`projectB`的镜像列表（通过UI或API） &#x20;

✅ TeamA用户推送镜像到`projectB`时返回403错误 &#x20;

### **3. 运维团队只读审计验证** &#x20;

#### **场景：运维查看所有项目但无法修改** &#x20;

切换运维用户登录docker

```bash 
docker login -u ops-user  harbor.company.com
```


登录成功

```markdown 
WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
Configure a credential helper to remove this warning. See
https://docs.docker.com/engine/reference/commandline/login/#credential-stores

Login Succeeded

```


```bash 
docker pull harbor.zx/projecta/prepare:v2.12.4
docker pull harbor.zx/projectb/nginx-photon:v2.12.4

```


都正常拉取

```docker 
v2.12.4: Pulling from projecta/prepare
259e20632a89: Pull complete
9d3c23c4fe1a: Pull complete
48e220409d89: Pull complete
a23d24267516: Pull complete
a523ceca9c6b: Pull complete
b91679e66cad: Pull complete
dee0791cf07d: Pull complete
6b240c6cb11d: Pull complete
fae1a7290768: Pull complete
48178d0316e5: Pull complete
Digest: sha256:43314c25e3bc1e5bdc99dd9a6e1834f78323986a777fe415ab333d01477c68c2
Status: Downloaded newer image for harbor.zx/projecta/prepare:v2.12.4



```


```docker 
v2.12.4: Pulling from projectb/nginx-photon
259e20632a89: Already exists
d62116f31987: Pull complete
Digest: sha256:771ba28904c22352cf6a60dbff178355636002fab4c91dfe9c126e0076b194d9
Status: Downloaded newer image for harbor.zx/projectb/nginx-photon:v2.12.4
harbor.zx/projectb/nginx-photon:v2.12.4

```


测试推送镜像到projectA

```bash 
docker tag ubuntu/squid:latest harbor.zx/projecta/squid:latest
docker push harbor.zx/projecta/squid:latest

```


输出

```text 
5bdca3f2dcb6: Preparing
b9411ff50c32: Preparing
3abdd8a5e7a8: Preparing
unauthorized: unauthorized to access repository: projecta/squid, action: push: unauthorized to access repository: projecta/squid, action: push

```


**验证点**： &#x20;

✅ 可在UI中查看所有项目的镜像列表和日志 &#x20;

✅ 任何推送/删除操作均被拒绝 &#x20;

### **4. 安全团队漏洞扫描验证** &#x20;

#### **场景：安全团队执行扫描并**查看报告

用sec-user用户登录，并对projectA镜像进行扫描

![](image/image_8kI9RC82by.png)

查看漏洞报告

![](image/image_twu3IRNF0_.png)

**验证点**： &#x20;

✅ 可查看所有项目的CVE报告 &#x20;

## **💡 实验结论** &#x20;

通过本实验验证了： &#x20;

1. **项目级隔离**：有效防止开发团队间的越权访问，提供项目之间的隔离性和安全性。
2. **角色权限粒度**：满足运维/安全团队的差异化需求，但是无法满足自定义角色的场景。

📌 **延伸思考**：

1. 如何结合Kubernetes的`ImagePullSecrets`实现更细粒度的集群侧权限控制？
2. 在CICD场景中，如何管理用户推送镜像？

***

# 总结

Harbor 通过**项目**和**角色权限管理**提供了强大的多租户隔离功能，帮助企业管理多个团队或项目的容器镜像。通过对项目进行隔离和对用户进行权限控制，可以确保不同团队的镜像不会相互影响，从而增强安全性。在实际应用中，企业可能需要对某些功能的权限授权，但是Harbor只能提供公开的角色给用户授权，因此无法满足这部分的权限管理要求。

***

# **参考文档**：

- \[1] [Harbor 官方文档 - Managing Users](https://goharbor.io/docs "Harbor 官方文档 - Managing Users")
- \[2] [Harbor 官方文档 - Role-Based Access Control](https://goharbor.io/docs "Harbor 官方文档 - Role-Based Access Control")
- \[3] [harbor-cli](https://github.com/goharbor/harbor-cli "harbor-cli")
- \[4] harbor权限表
  | 功能                | 有限访客 (Limited Guest) | 访客 (Guest) | 开发者 (Developer) | 维护者 (Maintainer) | 项目管理员 (Project Admin) |
  | ----------------- | -------------------- | ---------- | --------------- | ---------------- | --------------------- |
  | 查看项目配置            | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 编辑项目配置            |                      |            |                 |                  | ✔️                    |
  | 查看项目成员列表          |                      | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 创建/编辑/删除项目成员      |                      |            |                 |                  | ✔️                    |
  | 查看项目日志列表          |                      | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 查看项目复制列表          |                      |            |                 | ✔️               | ✔️                    |
  | 查看项目复制任务列表        |                      |            |                 |                  | ✔️                    |
  | 查看项目标签列表          |                      |            |                 | ✔️               | ✔️                    |
  | 创建/编辑/删除项目标签      |                      |            |                 | ✔️               | ✔️                    |
  | 查看仓库列表            | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 创建仓库              |                      |            | ✔️              | ✔️               | ✔️                    |
  | 编辑/删除仓库           |                      |            |                 | ✔️               | ✔️                    |
  | 查看镜像列表            | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 重新标记镜像            |                      | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 拉取镜像              | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 推送镜像              |                      |            | ✔️              | ✔️               | ✔️                    |
  | 扫描/删除镜像           |                      |            |                 | ✔️               | ✔️                    |
  | 查看镜像漏洞列表          | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 创建项目漏洞列表          |                      |            | ✔️              | ✔️               | ✔️                    |
  | 查看项目漏洞列表          |                      |            | ✔️              | ✔️               | ✔️                    |
  | 导出项目漏洞列表          |                      |            | ✔️              | ✔️               | ✔️                    |
  | 查看镜像构建历史          | ✔️ ✔                 | ️          | ✔️              | ✔️               | ✔️                    |
  | 添加/移除镜像标签         |                      |            | ✔️              | ✔️               | ✔️                    |
  | 查看 Helm 图表列表      | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 下载 Helm 图表        | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 上传 Helm 图表        |                      |            | ✔️              | ✔️               | ✔️                    |
  | 删除 Helm 图表        |                      |            |                 | ✔️               | ✔️                    |
  | 查看 Helm 图表版本列表    | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 下载 Helm 图表版本      | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 上传 Helm 图表版本      |                      |            | ✔️              | ✔️               | ✔️                    |
  | 删除 Helm 图表版本      |                      |            |                 | ✔️               | ✔️                    |
  | 添加/移除 Helm 图表版本标签 |                      |            | ✔️              | ✔️               | ✔️                    |
  | 查看项目机器人列表         |                      |            |                 | ✔️               | ✔️                    |
  | 创建/编辑/删除项目机器人     |                      |            |                 |                  | ✔️                    |
  | 查看已配置的 CVE 允许列表   | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 创建/编辑/移除 CVE 允许列表 |                      |            |                 |                  | ✔️                    |
  | 查看 Webhook 事件     |                      |            |                 | ✔️               | ✔️                    |
  | 添加新的 Webhook 事件   |                      |            |                 |                  | ✔️                    |
  | 启用/停用 Webhook     |                      |            |                 |                  | ✔️                    |
  | 创建/删除标签保留规则       |                      |            | ✔️              | ✔️               | ✔️                    |
  | 启用/停用标签保留规则       |                      |            | ✔️              | ✔️               | ✔️                    |
  | 创建/删除标签不可变规则      |                      |            |                 | ✔️               | ✔️                    |
  | 启用/停用标签不可变规则      |                      |            |                 | ✔️               | ✔️                    |
  | 查看项目配额            | ✔️                   | ✔️         | ✔️              | ✔️               | ✔️                    |
  | 编辑项目配额            |                      |            |                 |                  |                       |
  | 删除项目              |                      |            |                 |                  | ✔️                    |

***

&#x20;🚀  **Happy Cloud Native Journey!**    如果您对 Harbor 的升级或迁移有任何疑问，欢迎在评论区留言！同时，您也可以关注我的博客，获取更多云原生技术实践分享。
