# 一文学会基于K8S部署Kuboard可视化工具

## 目录

- [概念](#概念)
  - [KubeDashboard](#KubeDashboard)
    - [2. Kuboard简介](#2-Kuboard简介)
    - [3. KubeDashboard与Kuboard对比](#3-KubeDashboard与Kuboard对比)
- [Kuboard部署实践](#Kuboard部署实践)
  - [使用 hostPath 为Kuboard提供持久化](#使用-hostPath-为Kuboard提供持久化)
    - [安装Kuboard](#安装Kuboard)
    - [登录Kuboard页面](#登录Kuboard页面)
    - [安装agent](#安装agent)
    - [按照提示检查集群状态](#按照提示检查集群状态)
    - [安装集群监控插件（可选）](#安装集群监控插件可选)
    - [尝试部署一个deployment](#尝试部署一个deployment)
  - [方法二：使用 StorageClass 提供持久化](#方法二使用-StorageClass-提供持久化)
    - [下载yaml配置](#下载yaml配置)
    - [编辑yaml配置文件](#编辑yaml配置文件)
    - [部署问题整理](#部署问题整理)
      - [部署后发现etcd集群报错](#部署后发现etcd集群报错)
      - [部署后kuboard-v3出现pending状态](#部署后kuboard-v3出现pending状态)
      - [部署后出现初始化失败问题](#部署后出现初始化失败问题)
- [总结](#总结)
- [参考资料：](#参考资料)

> ❤️ 摘要：KubeDashboard和Kuboard是两种常见的Kubernetes可视化管理工具，它们帮助用户更直观地管理和监控Kubernetes集群。本文将介绍这两种工具的功能和组件，并对比它们的优势与适用场景，接着带着读者一步一步完成Kuboard的搭建和简单应用。本文适合希望深入了解并部署 Kubernetes可视化的开发者或者系统管理员阅读。

***

# 概念

## KubeDashboard

![](image/image_B4ccnC1Y3j.png)

KubeDashboard是Kubernetes官方提供的可视化界面，主要用于管理集群的资源和应用。它可以简化系统管理员的操作并能直观管理Kubernetes集群，例如创建、监控Pod、Service、deployment等资源，查看日志、管理命名空间等任务。

**主要功能：**

- 查看集群资源（如Pod、Deployment、DaemonSet等）。
- 管理工作负载：创建、更新和删除工作负载资源。
- 查看应用程序的运行状态、日志和事件。
- 管理Kubernetes凭证，例如ConfigMap和Secret。

### 2. Kuboard简介

![](image/image_PLX0FFyp5U.png)

Kuboard是一款国产的基于Kubernetes的用户友好型可视化管理工具，尤其适合初学者和中小型团队。它支持丰富的集群监控、资源管理以及滚动更新、蓝绿部署等功能，并且具备相对简单的安装过程和优秀的用户体验。

**主要功能：**

- 支持丰富的可视化集群监控。
- 管理多集群、多租户。
- 提供详细的Pod调度、服务发现等可视化。
- 支持CI/CD集成，提供流水线执行可视化。
- 提供了更为复杂的部署操作，如蓝绿部署、滚动更新。

### 3. KubeDashboard与Kuboard对比

| 对比项         | KubeDashboard                       | Kuboard                                |
| ----------- | ----------------------------------- | -------------------------------------- |
| **提供商**     | Kubernetes官方                        | 国内优秀项目，针对企业需求优化                        |
| **用户体验**    | 简洁，功能有限，适合基础操作                      | 界面更加友好，功能丰富，适合复杂场景                     |
| **登录方式**    | 只能通过 Bearer Token 或 kubeconfig 文件登录 | 支持账密登录、LDAP集中认证、Github/Gitlab单点登录等多种方式 |
| **部署复杂度**   | 简单，目前仅支持基于 Helm 的安装                 | 简单，支持docker、Kubernetes等多种方式部署          |
| **功能深度**    | 提供基础的集群管理和监控功能                      | 支持多集群、多租户及更复杂的部署方式                     |
| **CI/CD支持** | 无CI/CD功能                            | 支持CI/CD集成，适合DevOps场景                   |
| **适用场景**    | 适合初学者，快速了解K8s资源情况                   | 适合企业级应用，支持更复杂的K8s运维管理                  |

***

# Kuboard部署实践

> ❔ 说明：Kuboard官方强烈建议您使用 `docker run`或者 `static-pod`的方式安装 kuboard，我默认认为我的读者都是有一定的Linux和Kubernetes基础的， 所以以下演示案例会将Kuboard部署在Kubernetes集群中。

## 使用 hostPath 为Kuboard提供持久化

### 安装Kuboard

下载yaml部署文件

```bash 
curl -o kuboard-v3.yaml https://addons.kuboard.cn/kuboard/kuboard-v3-swr.yaml
```


修改kuboard-v3.yaml

```bash 
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kuboard-v3-config
  namespace: kuboard
data:
  # [common]
  # 修改Kuboard的启动端口
  KUBOARD_SERVER_NODE_PORT: '30088'

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    k8s.kuboard.cn/name: kuboard-v3
  name: kuboard-v3
  namespace: kuboard
spec:
  ports:
    - name: web
      # 修改Kuboard的暴露端口
      nodePort: 30088

```


- 镜像替换成私有仓库地址

```bash 
---
containers:
        - env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: HOSTIP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
          # 修改私有仓库地址
          image: 'harbor.zx/hcie/etcd-host:3.4.16-2'
          # 修改镜像拉取策略
          imagePullPolicy: IfNotPresent

---
      containers:
        - env:
            - name: HOSTIP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: kuboard-v3-config
          # 修改私有仓库地址
          image: 'harbor.zx/hcie/kuboard:v3'
          # 修改镜像拉取策略
          imagePullPolicy: IfNotPresent

```


部署节点亲和性

```bash 
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: node-role.kubernetes.io/master
                    operator: Exists
              weight: 100
            - preference:
                matchExpressions:
                  - key: node-role.kubernetes.io/control-plane
                    operator: Exists
              weight: 100

```


> ❔ 说明：kuboard会部署在有`node-role.kubernetes.io/master`、`node-role.kubernetes.io/control-plane`这两个标签的节点上。**关于节点亲和度，后面文章会详细说明，敬请期待**。

查看节点的label：

```bash 
[root@k8s-master1 ~]# kubectl get node --show-labels |grep node-role

```


- 发现master节点并没有这两个标签，可能是二进制部署k8s环境默认没有这两个label

为master节点添加label，执行以下命令：

```bash 
kubectl label node k8s-master${i} node-role.kubernetes.io/master=

kubectl label node k8s-master${i} node-role.kubernetes.io/control-plane=

```


如果您的Kubernetes集群有多个master节点，可以执行for循环添加label到全部节点：

```bash 
for i in {1..3};do kubectl label node k8s-master${i} node-role.kubernetes.io/master=;done

for i in {1..3};do kubectl label node k8s-master${i} node-role.kubernetes.io/control-plane=;done

```


部署kuboard-v3

```bash 
kubectl apply -f kuboard-v3.yaml
```


查看部署情况

```bash 
[root@k8s-master1 ~]# kubectl -n kuboard get pod
NAME                         READY   STATUS    RESTARTS   AGE
kuboard-etcd-54hsl           1/1     Running   0          3m
kuboard-etcd-tndsc           1/1     Running   0          3m
kuboard-etcd-ttljf           1/1     Running   0          3m
kuboard-v3-5c745bfc9-pfvcc   1/1     Running   0          3m

```


### 登录Kuboard页面

- 在浏览器中打开链接 `http://your-node-ip-address:30088`
- 输入初始用户名和密码，并登录
  - 用户名： `admin`
  - 密码： `Kuboard123`

![](image/image_KEn94W2vIj.png)

- 点击default集群，然后按提示步骤操作即可

### 安装agent

- 按照指示，在将要被导入的 Kubernetes 集群执行如下指令，以便安装 kuboard-agent

```bash 
curl -k 'http://*********:30088/kuboard-api/cluster/default/kind/KubernetesCluster/default/resource/installAgentToKubernetes?token=JVHHCRkH60U2dpbtUmrNjMm74s1F82bg' > kuboard-agent.yaml

```


修改kuboard-agent.yaml

```bash 
  # 修改私有镜像仓库
  image: "harbor.zx/hcie/kuboard-agent:v3"
  # 修改镜像拉取策略
  imagePullPolicy: IfNotPresent

```


部署agent

```bash 
kubectl apply -f ./kuboard-agent.yaml
```


### 按照提示检查集群状态

检查 kuboard-agent 状态

```bash 
[root@k8s-master1 ~]# kubectl get pods -n kuboard -o wide -l "k8s.kuboard.cn/name in (kuboard-agent, kuboard-agent-2)"
NAME                               READY   STATUS    RESTARTS   AGE   IP              NODE          NOMINATED NODE   READINESS GATES
kuboard-agent-2-67f598cf49-pr6j8   1/1     Running   0          31s   *************   k8s-master2   <none>           <none>
kuboard-agent-6cc557997f-htjhp     1/1     Running   0          31s   *************   k8s-master2   <none>           <none>

```


**测试连通性 UDP**

先安装nc

```bash 
yum -y install nc

```


执行以下命令测试

```bash 
[root@k8s-master1 ~]# nc -vuz ********* 30081
Ncat: Version 7.50 ( https://nmap.org/ncat )
Ncat: Connected to *********:30081.
Ncat: UDP packet sent successfully
Ncat: 1 bytes sent, 0 bytes received in 2.01 seconds.

```


检查 kuboard-agent 日志

```bash 
[root@k8s-master1 ~]# kubectl logs -f  -n kuboard -l "k8s.kuboard.cn/name in (kuboard-agent, kuboard-agent-2)"
Start nginx
Start nginx
nameserver is [************]
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:   export GIN_MODE=release
 - using code:  gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /check-certificates       --> main.checkCerts (3 handlers)
[GIN-debug] Listening and serving HTTP on :5000
nameserver is [************]
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:   export GIN_MODE=release
 - using code:  gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /check-certificates       --> main.checkCerts (3 handlers)
[GIN-debug] Listening and serving HTTP on :5000

```


最终效果

![](image/image_bxTxmcSapH.png)

### 安装集群监控插件（可选）

> ❔ 说明：&#x20;

- [metrics-server](https://github.com/kubernetes-incubator/metrics-server "metrics-server") 用于提供度量信息
- [metrics-scraper](https://github.com/kubernetes-sigs/dashboard-metrics-scraper "metrics-scraper") 用于存储最近一段时间内的 CPU/内存度量信息，以便在 kuboard 中展示 CPU/内存的曲线图

> 路径：进入default集群→安装metrics-scraper&#x20;

选择华为云源进行安装

![](image/image__CpxD2unwI.png)

点击应用

![](image/image_W5Pn0tlwlE.png)

两个插件状态是`True`，证明安装成功。

![](image/image_x4-xC80fNv.png)

查看集群负载情况

> 路径： 集群管理→概要→master1节点

![](image/image_1MZw2zoJIE.png)

点击master1节点，可以看到节点的负载曲线，以及具体监控数据

![](image/image_ED9W0HDsMu.png)

### 尝试部署一个deployment

> 路径： 名称空间→default命名空间

![](image/image_-Wf8CiTOVN.png)

使用从yaml文件创建

![](image/image_mbzNel08QI.png)

导入yaml文件

- demo.yaml内容：

```bash 
apiVersion: apps/v1
kind: Deployment
metadata:
# 定义Deployment的名字
  name: busybox-deployment
  labels:
    app: demo
spec:
  # 定义副本数
  replicas: 1
  selector:
    matchLabels:
      app: demo
  template:
    metadata:
    # 与选择器指定label匹配
      labels:
        app: demo
    spec:
      containers:
      # pod名字，可自定义
      - name: busybox
      # 镜像源， 这里设置私有镜像源
        image: harbor.zx/hcie/busybox:1.29-2
        args: ["/bin/sh", "-c", "sleep 3600"]

```


![](image/image_0bhRWd9Lg2.png)

点击确认，等deployment创建成功，尝试登录pod

![](image/image_vPuDWClTGz.png)

选择`sh`登录

![](image/image_Wl40F8kKQA.png)

正常登录，并能执行指令

![](image/image_g8lOKuKH4A.png)

删除demo-deployment

> 路径： 工作负载→删除

![](image/image_xPnH8SIbBg.png)

输出deployment名称确认删除

![](image/image_U55B5dL5zQ.png)

点击应用

![](image/image_UJWaMYdCG4.png)

最后完成删除

![](image/image_iaxz6EukVm.png)

## 方法二：使用 StorageClass 提供持久化

> ❔ 说明:  这个方法使用 StorageClass 动态创建 PV 为 etcd 提供数据卷, 演示案例使用nfs作为StorageClass，但是该部署方式官方不推荐，实测发现确实存在不少问题， 仅当学习研究，**关于PV、PVC、StorageClass，后续会出文章详细介绍。**

### 下载yaml配置

```bash 
curl -o kuboard-v3-storage-class.yaml https://addons.kuboard.cn/kuboard/kuboard-v3-storage-class.yaml
```


### 编辑yaml配置文件

修改KUBOARD\_ENDPOINT字段

```bash 
 7 ---
 apiVersion: v1
 kind: ConfigMap
 metadata:
   name: kuboard-v3-config
   namespace: kuboard
 data:
    # 关于如下参数的解释，请参考文档 https://kuboard.cn/install/v3/install-built-in.html
    # [common]
    # 修改访问地址和端口
   KUBOARD_ENDPOINT: 'http://192.168.3.41:30088'  

```


添加etcd的亲和性， 必须部署在master节点

```bash 
 77       affinity:
 78         nodeAffinity:
 79           requiredDuringSchedulingIgnoredDuringExecution:
 80             nodeSelectorTerms:
 81               - matchExpressions:
 82                   - key: node-role.kubernetes.io/master
 83                     operator: Exists
 84               - matchExpressions:
 85                   - key: node-role.kubernetes.io/control-plane
 86                     operator: Exists
 87               - matchExpressions:
 88                   - key: k8s.kuboard.cn/role
 89                     operator: In
 90                     values:
 91                       - etcd

```


- 说明与上面一样

修改私有仓库

```bash 
 # etcd镜像
 92       containers:
 93       - name: kuboard-etcd
 94         image: harbor.zx/hcie/etcd:v3.4.14
 ---
 # kuboard镜像
194           image: 'harbor.zx/hcie/kuboard:v3'
195           imagePullPolicy: Always


```


修改dns策略，使用k8s集群dns

```bash 
# 新加一行
121       dnsPolicy: ClusterFirst
# 新加一行
200       dnsPolicy: ClusterFirst


```


全部配置

```bash 
---
apiVersion: v1
kind: Namespace
metadata:
  name: kuboard

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kuboard-v3-config
  namespace: kuboard
data:
  # 关于如下参数的解释，请参考文档 https://kuboard.cn/install/v3/install-built-in.html
  # [common]
  KUBOARD_ENDPOINT: 'http://192.168.3.41:30088'
  KUBOARD_AGENT_SERVER_UDP_PORT: '30081'
  KUBOARD_AGENT_SERVER_TCP_PORT: '30081'
  KUBOARD_SERVER_LOGRUS_LEVEL: info  # error / debug / trace
  # KUBOARD_AGENT_KEY 是 Agent 与 Kuboard 通信时的密钥，请修改为一个任意的包含字母、数字的32位字符串，此密钥变更后，需要删除 Kuboard Agent 重新导入。
  KUBOARD_AGENT_KEY: 32b7d6572c6255211b4eec9009e4a816

  # 关于如下参数的解释，请参考文档 https://kuboard.cn/install/v3/install-gitlab.html
  # [gitlab login]
  # KUBOARD_LOGIN_TYPE: "gitlab"
  # KUBOARD_ROOT_USER: "your-user-name-in-gitlab"
  # GITLAB_BASE_URL: "http://gitlab.mycompany.com"
  # GITLAB_APPLICATION_ID: "7c10882aa46810a0402d17c66103894ac5e43d6130b81c17f7f2d8ae182040b5"
  # GITLAB_CLIENT_SECRET: "77c149bd3a4b6870bffa1a1afaf37cba28a1817f4cf518699065f5a8fe958889"

  # 关于如下参数的解释，请参考文档 https://kuboard.cn/install/v3/install-github.html
  # [github login]
  # KUBOARD_LOGIN_TYPE: "github"
  # KUBOARD_ROOT_USER: "your-user-name-in-github"
  # GITHUB_CLIENT_ID: "********************"
  # GITHUB_CLIENT_SECRET: "ff738553a8c7e9ad39569c8d02c1d85ec19115a7"

  # 关于如下参数的解释，请参考文档 https://kuboard.cn/install/v3/install-ldap.html
  # [ldap login]
  # KUBOARD_LOGIN_TYPE: "ldap"
  # KUBOARD_ROOT_USER: "your-user-name-in-ldap"
  # LDAP_HOST: "ldap-ip-address:389"
  # LDAP_BIND_DN: "cn=admin,dc=example,dc=org"
  # LDAP_BIND_PASSWORD: "admin"
  # LDAP_BASE_DN: "dc=example,dc=org"
  # LDAP_FILTER: "(objectClass=posixAccount)"
  # LDAP_ID_ATTRIBUTE: "uid"
  # LDAP_USER_NAME_ATTRIBUTE: "uid"
  # LDAP_EMAIL_ATTRIBUTE: "mail"
  # LDAP_DISPLAY_NAME_ATTRIBUTE: "cn"
  # LDAP_GROUP_SEARCH_BASE_DN: "dc=example,dc=org"
  # LDAP_GROUP_SEARCH_FILTER: "(objectClass=posixGroup)"
  # LDAP_USER_MACHER_USER_ATTRIBUTE: "gidNumber"
  # LDAP_USER_MACHER_GROUP_ATTRIBUTE: "gidNumber"
  # LDAP_GROUP_NAME_ATTRIBUTE: "cn"

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kuboard-etcd
  namespace: kuboard
  labels:
    app: kuboard-etcd
spec:
  serviceName: kuboard-etcd
  replicas: 3
  selector:
    matchLabels:
      app: kuboard-etcd
  template:
    metadata:
      name: kuboard-etcd
      labels:
        app: kuboard-etcd
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node-role.kubernetes.io/master
                    operator: Exists
              - matchExpressions:
                  - key: node-role.kubernetes.io/control-plane
                    operator: Exists
              - matchExpressions:
                  - key: k8s.kuboard.cn/role
                    operator: In
                    values:
                      - etcd
      containers:
      - name: kuboard-etcd
        image: harbor.zx/hcie/etcd:v3.4.14
        ports:
        - containerPort: 2379
          name: client
        - containerPort: 2380
          name: peer
        env:
        - name: KUBOARD_ETCD_ENDPOINTS
          value: >-
            kuboard-etcd-0.kuboard-etcd:2379,kuboard-etcd-1.kuboard-etcd:2379,kuboard-etcd-2.kuboard-etcd:2379
        volumeMounts:
        - name: data
          mountPath: /data
        command:
          - /bin/sh
          - -c
          - |
            PEERS="kuboard-etcd-0=http://kuboard-etcd-0.kuboard-etcd:2380,kuboard-etcd-1=http://kuboard-etcd-1.kuboard-etcd:2380,kuboard-etcd-2=http://kuboard-etcd-2.kuboard-etcd:2380"
            exec etcd --name ${HOSTNAME} \
              --listen-peer-urls http://0.0.0.0:2380 \
              --listen-client-urls http://0.0.0.0:2379 \
              --advertise-client-urls http://${HOSTNAME}.kuboard-etcd:2379 \
              --initial-advertise-peer-urls http://${HOSTNAME}:2380 \
              --initial-cluster-token kuboard-etcd-cluster-1 \
              --initial-cluster ${PEERS} \
              --initial-cluster-state new \
              --data-dir /data/kuboard.etcd
      dnsPolicy: ClusterFirst
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      # 请填写一个有效的 StorageClass name
      storageClassName: nfs-class
      accessModes: [ "ReadWriteMany" ]
      resources:
        requests:
          storage: 5Gi


---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kuboard-data-pvc
spec:
  storageClassName: nfs-class
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: kuboard-etcd
  namespace: kuboard
spec:
  type: ClusterIP
  ports:
  - port: 2379
    name: client
  - port: 2380
    name: peer
  selector:
    app: kuboard-etcd

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: '9'
    k8s.kuboard.cn/ingress: 'false'
    k8s.kuboard.cn/service: NodePort
    k8s.kuboard.cn/workload: kuboard-v3
  labels:
    k8s.kuboard.cn/name: kuboard-v3
  name: kuboard-v3
  namespace: kuboard
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s.kuboard.cn/name: kuboard-v3
  template:
    metadata:
      labels:
        k8s.kuboard.cn/name: kuboard-v3
    spec:
      containers:
        - env:
            - name: KUBOARD_ETCD_ENDPOINTS
              value: >-
                kuboard-etcd-0.kuboard-etcd:2379,kuboard-etcd-1.kuboard-etcd:2379,kuboard-etcd-2.kuboard-etcd:2379
          envFrom:
            - configMapRef:
                name: kuboard-v3-config
          image: 'harbor.zx/hcie/kuboard:v3'
          imagePullPolicy: Always
          name: kuboard
          volumeMounts:
            - mountPath: "/data"
              name: kuboard-data
      dnsPolicy: ClusterFirst
      volumes:
      - name: kuboard-data
        persistentVolumeClaim:
          claimName: kuboard-data-pvc

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    k8s.kuboard.cn/workload: kuboard-v3
  labels:
    k8s.kuboard.cn/name: kuboard-v3
  name: kuboard-v3
  namespace: kuboard
spec:
  ports:
    - name: webui
      nodePort: 30088
      port: 80
      protocol: TCP
      targetPort: 80
    - name: agentservertcp
      nodePort: 30081
      port: 10081
      protocol: TCP
      targetPort: 10081
    - name: agentserverudp
      nodePort: 30081
      port: 10081
      protocol: UDP
      targetPort: 10081
  selector:
    k8s.kuboard.cn/name: kuboard-v3
  sessionAffinity: None
  type: NodePort

```


部署

```bash 
kubectl apply -f kuboard-v3-storage-class.yaml
```


### 部署问题整理

#### 部署后发现etcd集群报错

```bash 
root@k8s-master1 ~]# kubectl -n kuboard get pod
NAME                        READY   STATUS             RESTARTS     AGE
kuboard-etcd-0              0/1     CrashLoopBackOff   1 (8s ago)   13s
kuboard-etcd-1              0/1     CrashLoopBackOff   1 (3s ago)   9s
kuboard-v3-bd947757-6bw76   0/1     Pending            0            12s

```


查看etcd日志

```bash 
[root@k8s-master1 ~]# kubectl -n kuboard logs kuboard-etcd-0
[WARNING] Deprecated '--logger=capnslog' flag is set; use '--logger=zap' flag instead
2024-09-04 15:13:52.474082 I | etcdmain: etcd Version: 3.4.14
2024-09-04 15:13:52.474122 I | etcdmain: Git SHA: 8a03d2e96
2024-09-04 15:13:52.474127 I | etcdmain: Go Version: go1.12.17
2024-09-04 15:13:52.474134 I | etcdmain: Go OS/Arch: linux/amd64
2024-09-04 15:13:52.474140 I | etcdmain: setting maximum number of CPUs to 2, total number of available CPUs is 2
2024-09-04 15:13:52.475844 N | etcdmain: the server is already initialized as member before, starting as etcd member...
[WARNING] Deprecated '--logger=capnslog' flag is set; use '--logger=zap' flag instead
2024-09-04 15:13:52.479618 I | embed: name = kuboard-etcd-0
2024-09-04 15:13:52.479659 I | embed: data dir = /data/kuboard.etcd
2024-09-04 15:13:52.479672 I | embed: member dir = /data/kuboard.etcd/member
2024-09-04 15:13:52.479681 I | embed: heartbeat = 100ms
2024-09-04 15:13:52.479688 I | embed: election = 1000ms
2024-09-04 15:13:52.479696 I | embed: snapshot count = 100000
2024-09-04 15:13:52.479716 I | embed: advertise client URLs = http://kuboard-etcd-0.kuboard-etcd:2379
{"level":"info","ts":1725462832.4953966,"caller":"netutil/netutil.go:112","msg":"resolved URL Host","url":"http://kuboard-etcd-0:2380","host":"kuboard-etcd-0:2380","resolved-addr":"*************:2380"}
{"level":"info","ts":1725462832.4966922,"caller":"netutil/netutil.go:112","msg":"resolved URL Host","url":"http://kuboard-etcd-0.kuboard-etcd:2380","host":"kuboard-etcd-0.kuboard-etcd:2380","resolved-addr":"***********:2380"}
2024-09-04 15:13:52.498948 C | etcdmain: --initial-cluster has kuboard-etcd-0=http://kuboard-etcd-0.kuboard-etcd:2380 but missing from --initial-advertise-peer-urls=http://kuboard-etcd-0:2380 ("http://*************:2380"(resolved from "http://kuboard-etcd-0:2380") != "http://***********:2380"(resolved from "http://kuboard-etcd-0.kuboard-etcd:2380"))


```


**错误分析：**

etcd日志中提到的两个URL：

- `http://kuboard-etcd-0:2380` 解析为 `*************:2380`
- `http://kuboard-etcd-0.kuboard-etcd:2380` 解析为 `***********:2380`

这表明在 `--initial-cluster` 参数中定义的节点地址与 `--initial-advertise-peer-urls` 参数中定义的节点地址不一致。

etcd期望每个节点在集群初始化时使用一致的地址来通信，如果地址不匹配，etcd就无法正确地建立集群。

**解决方法：**

**检查配置文件或启动命令**：

- 检查etcd节点的启动参数，确保`--initial-cluster` 和 `--initial-advertise-peer-urls`中的地址一致。

```bash 
#  initial-advertise-peer-urls 添加后缀“.kuboard-etcd”
116               --initial-advertise-peer-urls http://${HOSTNAME} .kuboard-etcd :2380 \
```


重新部署kuboard

```bash 
kubectl delete -f kuboard-v3-storage-class.yaml
kubectl apply-f kuboard-v3-storage-class.yaml


```


#### 部署后kuboard-v3出现pending状态

查看pod的状态

```bash 
[root@k8s-master1 ~]# kubectl get pod -n kuboard
NAME                         READY   STATUS    RESTARTS   AGE
kuboard-etcd-0               1/1     Running   0          6m28s
kuboard-etcd-1               1/1     Running   0          6m24s
kuboard-etcd-2               1/1     Running   0          6m19s
kuboard-v3-68f959f84-qjpxc   0/1     Pending   0          6m27s

```


查看pod的日志

```bash 
Events:
  Type     Reason            Age                From               Message
  ----     ------            ----               ----               -------
  Warning  FailedScheduling  83s (x5 over 99s)  default-scheduler  0/5 nodes are available: persistentvolumeclaim "kuboard-data-pvc" not found. preemption: 0/5 nodes are available: 5 Preemption is not helpful for scheduling.

```


查看pv

```bash 
[root@k8s-master1 ~]# kubectl get pv -n kuboard
NAME                                       CAPACITY   ACCESS MODES   RECLAIM POLICY   STATUS   CLAIM                         STORAGECLASS   VOLUMEATTRIBUTESCLASS   REASON   AGE
pvc-979b8c73-927c-49bd-99f4-6785fb735cc2   10Gi       RWO            Delete           Bound     default/kuboard-data-pvc       nfs-class      <unset>                          5m4s
pvc-d2f7cf5b-6c9f-4ee6-bdbb-41c7e93306fe   5Gi        RWX            Delete           Bound    kuboard/data-kuboard-etcd-0   nfs-class      <unset>                          5m4s
pvc-d789ff47-9f2d-4476-b8da-74c841ae73fe   5Gi        RWX            Delete           Bound    kuboard/data-kuboard-etcd-1   nfs-class      <unset>                          5m
pvc-fe5478cb-95df-43a2-88b9-be8d01ce4d4d   5Gi        RWX            Delete           Bound    kuboard/data-kuboard-etcd-2   nfs-class      <unset>                          4m55s
```


查看pvc

```bash 
[root@k8s-master1 ~]# kubectl get pvc
NAME               STATUS   VOLUME                                     CAPACITY   ACCESS MODES   STORAGECLASS   VOLUMEATTRIBUTESCLASS   AGE
kuboard-data-pvc   Bound    pvc-979b8c73-927c-49bd-99f4-6785fb735cc2   10Gi       RWO            nfs-class      <unset>                 6m3s

```


**错误分析：**

- pvc、pv创建在default命名空间
- pod无法找到对应的pvc、pv，所以一直等待pv、pvc的创建

**解决方法：**

修改yaml配置

在pvc添加namespace

```bash 
139   namespace: kuboard

```


重新部署kuboard

```bash 
kubectl delete -f kuboard-v3-storage-class.yaml
kubectl apply-f kuboard-v3-storage-class.yaml

```


#### 部署后出现初始化失败问题

查看pod状态

```bash 
[root@k8s-master1 ~]# kubectl get pod -n kuboard
NAME                         READY   STATUS    RESTARTS   AGE
kuboard-etcd-0               1/1     Running   0          8h
kuboard-etcd-1               1/1     Running   0          8h
kuboard-etcd-2               1/1     Running   0          8h
kuboard-v3-68f959f84-gljdv   1/1     Running   0          8h

```


- 但是无法访问kuboard

查看pod的log

```bash 
[LOG] 2024/09/05 - 00:16:32.238   | /common/etcd.client_config                                    24 |  info | KUBOARD_ETCD_ENDPOINTS=[kuboard-etcd-0.kuboard-etcd:2379 kuboard-etcd-1.kuboard-etcd:2379 kuboard-etcd-2.kuboard-etcd:2379]
[LOG] 2024/09/05 - 00:16:32.246   | /common/etcd.client_config                                    52 |  info | {[kuboard-etcd-0.kuboard-etcd:2379 kuboard-etcd-1.kuboard-etcd:2379 kuboard-etcd-2.kuboard-etcd:2379] 0s 1s 0s 0s 0 0 <nil>   false [] <nil> <nil> <nil> false}
[LOG] 2024/09/05 - 00:16:32.248   | /initializekuboard.InitializeEtcd                             39 |  info | 初始化 ./init-etcd-scripts/audit-policy-once.yaml
{"level":"warn","ts":"2024-09-05T00:16:33.916+0800","caller":"clientv3/retry_interceptor.go:61","msg":"retrying of unary invoker failed","target":"endpoint://client-db937380-f52a-46ea-b0f9-6450eaf84390/kuboard-etcd-0.kuboard-etcd:2379","attempt":0,"error":"rpc error: code = DeadlineExceeded desc = context deadline exceeded"}
failed to initialize server: server: failed to list connector objects from storage: context deadline exceeded

```


- 发现client连接etcd失败，导致初始化失败
- 方法二是不推荐的方法， 可能是不维护了， 部署过程存在不少问题， 导致最终部署不成功。

# 总结

KubeDashboard适合用户进行基础的Kubernetes集群管理与监控，适合小型开发团队和初学者。而Kuboard则提供了更强大的功能，支持企业级场景的多集群管理与复杂的部署操作，适合需要更多集群管理能力的用户。您可以根据具体场景需求，选择合适的可视化工具能够提升Kubernetes的运维效率。

# 参考资料：

- [Kubernetes Dashboard 官方文档](https://kubernetes.io/docs/tasks/access-application-cluster/web-ui-dashboard/ "Kubernetes Dashboard 官方文档")
- [Kuboard 官方网站](https://kuboard.cn/ "Kuboard 官方网站")
