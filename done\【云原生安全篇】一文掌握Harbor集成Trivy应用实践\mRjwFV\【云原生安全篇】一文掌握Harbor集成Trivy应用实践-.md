# 【云原生安全篇】一文掌握Harbor集成Trivy应用实践&#x20;

## 目录

- [1 概念](#1-概念)
  - [1.1 什么是 Harbor 和 Trivy？](#11-什么是-Harbor-和-Trivy)
    - [1.1.1 Harbor ](#111-Harbor-)
    - [1.1.2 Trivy ](#112-Trivy-)
  - [1.2 Harbor 与 Trivy 的关系](#12-Harbor-与-Trivy-的关系)
    - [Trivy 在 Harbor 中的作用：](#Trivy-在-Harbor-中的作用)
  - [1.3 镜像扫描工作流程](#13-镜像扫描工作流程)
- [2 实战案例：在Harbor 配置 Trivy ](#2-实战案例在Harbor-配置-Trivy-)
  - [2.1 环境准备](#21-环境准备)
  - [2.2 步骤 1：启用 Trivy 作为漏洞扫描工具](#22-步骤-1启用-Trivy-作为漏洞扫描工具)
  - [2.3 步骤 2：配置扫描策略](#23-步骤-2配置扫描策略)
  - [2.4 步骤 3：推送镜像到 Harbor](#24-步骤-3推送镜像到-Harbor)
  - [2.5 步骤 4：查看漏洞扫描结果](#25-步骤-4查看漏洞扫描结果)
  - [2.6 步骤5： 其他操作](#26-步骤5-其他操作)
- [3 总结](#3-总结)
- [4 参考资料](#4-参考资料)

> ❤️ 摘要：随着容器技术的广泛应用，确保容器镜像的安全性变得至关重要。**Harbor** 是一个强大的企业级容器镜像仓库，而 **Trivy** 是一款高效的开源安全扫描工具。通过将 Trivy 与 Harbor 集成，企业可以在镜像上传过程中自动进行漏洞扫描，从而增强容器镜像的安全性。本文将简述 Harbor 和 Trivy 的应用，并给出详细的工作流程、配置步骤和验证方法。

***

# 1 概念

![](image/image_BbGVJK85JD.png)

## 1.1 什么是 Harbor 和 Trivy？

### 1.1.1 Harbor&#x20;

**Harbor** 是一个开源的企业级容器镜像仓库管理平台，旨在帮助组织安全高效地存储、管理和分发 Docker 容器镜像。Harbor 还支持多种集成工具，如漏洞扫描工具 Trivy，用于增强镜像安全。

### 1.1.2 Trivy&#x20;

**Trivy** 是由 Aqua Security 开发的开源安全扫描工具，能够扫描容器镜像、文件系统、基础设施即代码（IaC）等对象中的已知漏洞。Trivy 提供了快速、准确的扫描结果，帮助开发人员在开发早期识别潜在的安全风险。

> ❤️ 文档参考： 想详细了解Harbor，可以提前读《[一文读懂Harbor以及部署实践攻略](https://stevenzeng.blog.csdn.net/article/details/142024722 "一文读懂Harbor以及部署实践攻略")》； 想详细了解Trivy，可以提前读《【Kubernetes安全篇】一文读懂Trivy 》

## 1.2 Harbor 与 Trivy 的关系

Harbor 自 2.0 版本开始，默认支持与 **Trivy** 集成，作为其漏洞扫描工具。通过应用集成，当用户上传镜像到 Harbor 仓库时，Trivy 会自动扫描镜像中操作系统包和应用程序依赖中的已知漏洞，并生成详细的漏洞报告。

### **Trivy 在 Harbor 中的作用**：

- 在镜像推送到 Harbor 仓库后，Trivy 自动触发扫描。
- Harbor 将扫描结果存储，并可以通过 Web 界面查看扫描的详细信息。
- Harbor 支持为不同的项目设置不同的漏洞扫描策略（如阻止带有高危漏洞的镜像拉取）。

## 1.3 镜像扫描工作流程

下图展示了 Harbor 结合 Trivy 进行镜像安全扫描的工作流程：

```mermaid 
%%{
  init: {
    'theme': 'base',
    'themeVariables': {
      'primaryColor': '#BBDEFB',
      'primaryTextColor': '#607D8B',
      'primaryBorderColor': '#bbdefb',
      'lineColor': '#757575'
    }
  }
}%%
flowchart TD
    A[用户推送容器镜像到 Harbor] --> B[Harbor 存储镜像]
    B --> C[Trivy 自动扫描镜像漏洞]
    C --> D[Harbor 存储扫描结果]
    D --> E[用户查看漏洞扫描报告]
    C --> F{扫描发现高危漏洞?}
    F -->|是| G[Harbor 阻止镜像拉取]
    F -->|否| H[镜像可正常拉取]

```


> ❔ 流程说明：

1. 用户通过 Docker CLI 或 CI/CD 工具将容器镜像推送到 Harbor 容器镜像仓；
2. Harbor 在接收到镜像后，启动镜像存储过程，同时触发 Trivy 进行漏洞扫描；
3. Trivy 解析镜像中的操作系统包和应用依赖，与漏洞数据库检索比对，然后查找已知漏洞；
4. 漏洞扫描完成后，Harbor 将扫描结果存储到本地数据库，通过Web页面方式供管理员或开发者查看；
5. 如果扫描结果中包含高危漏洞，根据 Harbor 的安全策略，可能阻止该镜像的拉取，直到漏洞得到修复。

# 2 实战案例：在Harbor 配置 Trivy&#x20;

下面介绍如何配置 Harbor 与 Trivy 进行集成，确保每次镜像推送到 Harbor 后都能自动触发 Trivy 的漏洞扫描。

## 2.1 环境准备

- 提前部署并运行的 Harbor 实例（要求2.0 及以上版本）。
- Harbor 配置使用 Trivy 作为默认扫描器。

## 2.2 步骤 1：启用 Trivy 作为漏洞扫描工具

因为我是使用docker-compose官方方式部署，如果要安装trivy组件，需要先执行以下命令：

```bash 
cd /harbor
sudo ./install.sh --with-trivy

```


检查harbor的状态

```bash 
root@harbor:~/harbor/harbor# docker-compose ps
      Name                   Command                State                 Ports
------------------------------------------------------------------------------------------
harbor-core         /harbor/entrypoint.sh        Up (healthy)
harbor-db           /docker-entrypoint.sh 13     Up (healthy)
                    14
harbor-jobservice   /harbor/entrypoint.sh        Up (healthy)
harbor-log          /bin/sh -c /usr/local/bin/   Up (healthy)   127.0.0.1:1514->10514/tcp
                    ...
harbor-portal       nginx -g daemon off;         Up (healthy)
nginx               nginx -g daemon off;         Up (healthy)   0.0.0.0:80-
                                                                >8080/tcp,:::80->8080/tcp,
                                                                0.0.0.0:443-
                                                                >8443/tcp,:::443->8443/tcp
redis               redis-server                 Up (healthy)
                    /etc/redis.conf
registry            /home/<USER>/entrypoint.sh   Up (healthy)
registryctl         /home/<USER>/start.sh        Up (healthy)
trivy-adapter       /home/<USER>/entrypoint.s   Up (healthy)

```


- 看到harbor的组件比之前多个trivy的容器

可以进入trivy容器，查看trivy的信息

```bash 
root@harbor:~/harbor/harbor# docker exec -it trivy-adapter bash

```


查看版本和数据库

```bash 
scanner [ / ]$ trivy version
Version: 0.52.2
Vulnerability DB:
  Version: 2
  UpdatedAt: 2024-09-13 12:12:21.448692961 +0000 UTC
  NextUpdate: 2024-09-13 18:12:21.4486927 +0000 UTC
  DownloadedAt: 2024-09-13 15:19:40.854214676 +0000 UTC
Java DB:
  Version: 1
  UpdatedAt: 2024-09-13 01:08:50.455768046 +0000 UTC
  NextUpdate: 2024-09-16 01:08:50.455767885 +0000 UTC
  DownloadedAt: 2024-09-13 15:20:47.499356951 +0000 UTC

```


登录Harbor页面检查trivy应用是否启用；

点击“审查服务”，在界面中，可以看到Trivy已经作为默认的漏洞扫描器并状态是 **“启用”**。

![](image/image_XqQGL0ECFn.png)

![](image/image_vLssm_7F1Q.png)

## 2.3 步骤 2：配置扫描策略

为确保所有上传的镜像都能被自动扫描，你需要为项目设置自动扫描策略。

> 操作路径：项目→项目名称“hcie”→配置管理

![](image/image_ItdSzwb-QZ.png)

![](image/image_M-KTum8oeK.png)

找到 `漏洞扫描`，选择 `自动扫描镜像`，以确保每次推送镜像后，Trivy 会自动扫描该镜像。

![](image/image_hsWs7PDUrp.png)

## 2.4 步骤 3：推送镜像到 Harbor

在hcie项目中，先删除原镜像，重新上传一个nginx：1.26.1的镜像。

1. 登录到 Docker 客户端：
   ```bash 
   docker login harbor.zx
   ```

2. 标记要推送的镜像：
   ```bash 
   docker tag registry.cn-hangzhou.aliyuncs.com/hcie/nginx:1.26.1 harbor.zx/hcie/nginx:1.26.1
   ```

3. 推送镜像到 Harbor：
   ```bash 
   docker push harbor.zx/hcie/nginx:1.26.1
   ```


## 2.5 步骤 4：查看漏洞扫描结果

镜像推送完成后，Harbor 会自动触发 Trivy 进行漏洞扫描。您可以通过 Harbor 控制台查看扫描结果：

> 操作路径： 项目→项目名称“hcie”→名称hcie/nginx→Artifacts

![](image/image_eL31ijwgt9.png)

点击该镜像的标签（tag），在详情页面中，点击 `漏洞` 标签，查看 Trivy 生成的漏洞扫描报告。报告中列出了所有检测到的漏洞，按严重性分类，并提供相应的修复建议。

![](image/image_9xKYxBbxO4.png)

## 2.6 步骤5： 其他操作

关于漏洞扫描，还有一些功能介绍说明：

> 路径：审查服务→漏洞→定期扫描所有

- 手动扫描：这里你可以点击开始扫描， 手动触发对现有所有镜像的漏洞扫描；
- 定时扫描：也可以设置定时扫描策略，如按小时/天/周/自定义

![](image/image_THPUaR5FuL.png)

自定义任务，按crontab的格式：

![](image/image_RmShR77UTk.png)

在安全中心页面，可以看到全局镜像的扫描结果，以及数据可视化展示，包括漏洞总览、最危险的5个 Artifacts、漏洞详细列表。

![](image/image_u1MCBQ34Yq.png)

# 3 总结

通过将 **Harbor** 和 **Trivy** 集成，可以在容器镜像推送到镜像仓库的过程中自动执行漏洞扫描，确保开发和运维团队在开发生命周期能尽早发现并修复潜在的安全问题。

通过本文的配置和工作流程，您可以轻松设置 Harbor 和 Trivy 的集成，确保容器镜像在上线前通过严格的安全检查。

# 4 参考资料

\[1] [harbor-scanner-trivy的github项目](https://github.com/aquasecurity/harbor-scanner-trivy?tab=readme-ov-file#version-matrix "harbor-scanner-trivy的github项目")

\[2] [harbor脚本部署](https://goharbor.io/docs/2.1.0/install-config/run-installer-script/ "harbor脚本部署")

\[3][harbor漏洞扫描](https://goharbor.io/docs/2.1.0/administration/vulnerability-scanning/ "harbor漏洞扫描")
