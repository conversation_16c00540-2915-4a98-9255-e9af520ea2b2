# 一文掌握Harbor的后端存储管理

## 目录

- [概述](#概述)
  - [什么是Harbor](#什么是Harbor)
    - [Harbor 存储后端的多种配置指南](#Harbor-存储后端的多种配置指南)
    - [1. Harbor 存储后端概述](#1-Harbor-存储后端概述)
    - [2. 配置不同类型的存储后端](#2-配置不同类型的存储后端)
      - [2.1 配置 NFS 存储后端](#21-配置-NFS-存储后端)
        - [NFS配置步骤：](#NFS配置步骤)
      - [2.2 配置 Ceph 存储后端](#22-配置-Ceph-存储后端)
        - [Ceph配置步骤：](#Ceph配置步骤)
      - [2.4 配置阿里云 OSS 存储后端](#24-配置阿里云-OSS-存储后端)
        - [阿里云OSS配置步骤：](#阿里云OSS配置步骤)
    - [3. 各种存储后端的比较](#3-各种存储后端的比较)
    - [4. Harbor + 阿里云OSS 实践部署与运维](#4-Harbor--阿里云OSS-实践部署与运维)
      - [4.1 环境准备](#41-环境准备)
      - [4.2 Harbor 安装步骤](#42-Harbor-安装步骤)
      - [4.3 日常运维](#43-日常运维)
    - [总结](#总结)

![  ](image/harbor-logo1_4W04cvubGQ.png "  ")

📚  **博客主页：** \<font face="黑体" size=5.5>[**StevenZeng学堂**](https://blog.csdn.net/u013522701?spm=1010.2135.3001.5343 "StevenZeng学堂")\</font>

🎉  **博客专栏:** &#x20;

- [**一文读懂Kubernetes**](https://blog.csdn.net/u013522701/category_12778372.html?spm=1001.2014.3001.5482 "一文读懂Kubernetes")&#x20;
- [**一文读懂Harbor**](https://blog.csdn.net/u013522701/category_12778948.html?spm=1001.2014.3001.5482 "一文读懂Harbor")
- [**云原生安全实战指南**](https://blog.csdn.net/u013522701/category_12785632.html "云原生安全实战指南")&#x20;
- [**云原生存储实践指南**](https://blog.csdn.net/u013522701/category_12811050.html?spm=1001.2014.3001.5482 "云原生存储实践指南")

***

![](https://img-blog.csdnimg.cn/img_convert/c9fc944bbbaf0e567b4659df99169fed.gif)

***

> ❤️ 摘要： 上文讲解了Kubernetes的持久化存储-PV和PVC， 本文将讨论Kubernetes中的一种重要的存储类型：`emptyDir` 。通过实验演示它们在实际场景中的应用。本文适合具备一定Kubernetes基础的读者，并且对云原生容器存储和日志处理感兴趣的技术人员。

***

**💯 本文关联好文:**

- 《[一文读懂K8S的PV和PVC以及实践攻略](https://stevenzeng.blog.csdn.net/article/details/143006374 "一文读懂K8S的PV和PVC以及实践攻略")》
- 《[一文掌握Cephadm部署Ceph存储集群](https://stevenzeng.blog.csdn.net/article/details/143142428 "一文掌握Cephadm部署Ceph存储集群")》

***

✍🏻\*\* 本文知识点：\*\*

- 版本升级
- 镜像复制

***

# 概述

## 什么是Harbor

Harbor 是一个企业级的云原生容器镜像仓库，由 VMware 主导开发并贡献给 Cloud Native Computing Foundation (CNCF)。它通过为 Docker 镜像提供安全、高效的管理能力，帮助企业简化容器应用程序的交付流程。相比于传统的 Docker Registry，Harbor 提供了更多的企业级特性，如容器镜像仓库之间的镜像复制、用户管理、访问控制、漏洞扫描和镜像签名等功能。

### Harbor 存储后端的多种配置指南

**Harbor** 是一个高效的开源企业级容器镜像仓库，它支持多种类型的存储后端来保存容器镜像数据。在企业部署中，选择合适的存储后端至关重要，因为它会影响系统的可扩展性、可靠性和性能。本文将详细介绍如何配置不同类型的存储后端，如 **NFS**、**Ceph**、**AWS S3**、**阿里云OSS** 等，并比较它们的优缺点及适用场景。最后，我们将进行 **Harbor + 阿里云OSS** 的实践部署。

***

### 1. Harbor 存储后端概述

Harbor 支持以下几种存储后端：

- **文件系统（FileSystem）**：默认的本地存储方案，适合小规模的测试或开发环境。
- **NFS（网络文件系统）**：通过共享文件系统提供分布式存储。
- **Ceph**：分布式对象存储，适合大规模集群环境。
- **AWS S3**：Amazon提供的对象存储服务，适用于云原生架构。
- **阿里云OSS**：阿里云的对象存储服务，类似AWS S3，适合在阿里云上的云原生部署。

***

### 2. 配置不同类型的存储后端

#### 2.1 配置 NFS 存储后端

**NFS（Network File System）** 是通过网络共享文件系统的一种方案，适用于多个节点共享存储的场景。

##### NFS配置步骤：

1. **安装NFS服务器**（在NFS服务器上）：
   ```bash 
   sudo apt-get install nfs-kernel-server
   ```

2. **配置NFS共享目录**（例如 `/mnt/harbor`）：

   编辑 `/etc/exports` 文件，添加以下内容：
   ```bash 
   /mnt/harbor ***********/24(rw,sync,no_subtree_check)
   ```

   这里 `***********/24` 是允许访问的子网范围。
3. **启动NFS服务**：
   ```bash 
   sudo systemctl start nfs-kernel-server
   sudo exportfs -a
   ```

4. **配置Harbor使用NFS存储**：

   在 `harbor.yml` 文件中，配置 `storage_service` 使用 `nfs`：
   ```yaml 
   storage_service:
     filesystem:
       rootdirectory: /mnt/harbor
   ```

   挂载NFS目录到每个节点的 `/mnt/harbor` 路径：
   ```bash 
   sudo mount -t nfs <NFS_SERVER_IP>:/mnt/harbor /mnt/harbor
   ```


#### 2.2 配置 Ceph 存储后端

**Ceph** 是一个分布式对象存储系统，提供高可扩展性和高性能，适合大规模的存储集群。

##### Ceph配置步骤：

1. **安装并部署 Ceph 集群**，可以参考 Ceph 官方文档完成集群部署。
2. **在 Harbor 配置中使用 Ceph RADOS Gateway（RGW）**：

   编辑 `harbor.yml` 文件，将存储后端配置为 Ceph：
   ```yaml 
   storage_service:
     s3:
       region: "default"
       accesskey: "<your-ceph-access-key>"
       secretkey: "<your-ceph-secret-key>"
       bucket: "harbor-bucket"
       regionendpoint: "http://<ceph-rgw-endpoint>:8080"
       secure: false
       v4auth: true
   ```

3. **创建 Ceph 存储桶**：

   使用 `radosgw-admin` 创建存储桶并管理权限。

#### 2.4 配置阿里云 OSS 存储后端

**阿里云OSS（Object Storage Service）** 是阿里云提供的对象存储服务，类似于 AWS S3。它适用于中国市场的用户，并具有高可用性和低延迟的特点。

##### 阿里云OSS配置步骤：

1. **创建OSS Bucket**：

   在阿里云 OSS 管理控制台中创建一个存储桶，并选择合适的区域（Region）。
2. **获取AccessKey**：

   在阿里云控制台中进入 `AccessKey` 管理页面，获取 `AccessKey ID` 和 `AccessKey Secret`。
3. **配置Harbor使用OSS存储**：

   编辑 `harbor.yml` 文件，将存储后端配置为 OSS：
   ```yaml 
   storage_service:
     oss:
       accesskeyid: "<your-aliyun-access-key-id>"
       accesskeysecret: "<your-aliyun-access-key-secret>"
       region: "oss-cn-shanghai"
       bucket: "harbor-bucket"
       endpoint: "https://oss-cn-shanghai.aliyuncs.com"
       secure: true
   ```

4. **启动 Harbor**：

   保存 `harbor.yml` 文件后，重新启动 Harbor 服务：
   ```bash 
   sudo docker-compose down
   sudo docker-compose up -d
   ```


***

### 3. 各种存储后端的比较

| 存储类型       | 优点                       | 缺点                   | 适用场景                     |
| ---------- | ------------------------ | -------------------- | ------------------------ |
| **NFS**    | 简单易部署，适合共享存储             | 性能相对较低，在高并发场景下容易成为瓶颈 | 小规模企业或开发测试环境             |
| **Ceph**   | 高可用、高扩展性，支持块存储、文件存储、对象存储 | 部署复杂，运维成本高           | 大规模集群环境，尤其是需要高可用和海量存储的场景 |
| **AWS S3** | 高度可扩展，全球服务，集成方便          | 网络延迟较大（跨区域访问），费用可能较高 | 云原生应用、跨地域的企业应用           |
| **阿里云OSS** | 高可用、低延迟，适合中国市场，易于集成      | 主要适用于阿里云生态，跨云供应商不够灵活 | 使用阿里云服务的企业，尤其是面向中国市场的应用  |

***

### 4. Harbor + 阿里云OSS 实践部署与运维

在这一部分，我们将实际操作如何将 Harbor 部署到阿里云，并使用 OSS 作为 Harbor 的存储后端。

#### 4.1 环境准备

- **阿里云 ECS 实例**：用于部署 Harbor。
- **Docker 和 Docker Compose**：确保 ECS 实例上安装了 Docker 和 Docker Compose。
  ```bash 
  sudo apt update
  sudo apt install docker.io docker-compose
  ```

- **阿里云 OSS 存储桶**：在阿里云 OSS 控制台中创建一个新的存储桶，区域选择你 ECS 实例所在的区域，获取 `AccessKey` 和 `AccessSecret`。

#### 4.2 Harbor 安装步骤

1. **下载并配置 Harbor**：

   从 Harbor 官方网站下载 Harbor 安装包：
   ```bash 
   wget https://github.com/goharbor/harbor/releases/download/v2.7.0/harbor-online-installer-v2.7.0.tgz
   tar xvf harbor-online-installer-v2.7.0.tgz
   cd harbor
   ```

2. **配置 ****`harbor.yml`**** 文件**：

   编辑 `harbor.yml` 文件，配置存储服务为 OSS：
   ```yaml 
   storage_service:
     oss:
       accesskeyid: "<your-aliyun-access-key-id>"
       accesskeysecret: "<your-aliyun-access-key-secret>"
       region: "oss-cn-shanghai"
       bucket: "harbor-bucket"
       endpoint: "https://oss-cn-shanghai.aliyuncs.com"
       secure: true
   ```


3\.

**安装并启动 Harbor**：

执行以下命令安装并启动 Harbor：

```bash 
sudo ./install.sh
```


1. **访问 Harbor**：

   Harbor 安装完成后，可以通过浏览器访问 ECS 实例的 IP 地址来查看 Harbor 的控制台。默认情况下，登录账户为 `admin`，密码为配置文件中的 `admin_password`。

#### 4.3 日常运维

- **监控**：确保 OSS 存储桶的健康状态，可以通过阿里云 OSS 控制台查看存储使用情况。
- **备份与恢复**：阿里云 OSS 本身具有高可用和容灾能力，无需额外手动备份。不过，你可以使用 OSS 的生命周期策略或跨区域复制功能确保数据安全。
- **费用控制**：定期监控 OSS 的使用情况，避免不必要的费用增加。根据业务量调整存储桶的存储类型（标准、低频或归档存储）。

***

### 总结

通过将 Harbor 集成到不同的存储后端，如 NFS、Ceph、AWS S3 和阿里云 OSS，用户可以根据不同的需求选择最适合的存储解决方案。**NFS** 适合小规模环境，**Ceph** 适合大规模集群，**AWS S3** 和 **阿里云 OSS** 则适合云原生部署，特别是 **OSS** 在中国市场具有明显的优势。

在实践中，使用 **Harbor + 阿里云OSS** 的组合，不仅可以充分利用阿里云的强大存储能力，还能轻松实现镜像的高可用和高扩展性，为企业提供一套可靠的容器镜像管理解决方案。
