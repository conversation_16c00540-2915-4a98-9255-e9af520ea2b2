# 一文读懂StatefulSet以及实践攻略

## 目录

- [1 概述](#1-概述)
  - [1.1 什么是有状态应用和无状态应用](#11-什么是有状态应用和无状态应用)
  - [1.2 什么是StatefulSet](#12-什么是StatefulSet)
  - [1.3 StatefulSet 与 Deployment 的区别](#13-StatefulSet-与-Deployment-的区别)
  - [1.4 StatefulSet 的工作机制](#14-StatefulSet-的工作机制)
  - [1.5 适用场景](#15-适用场景)
  - [1.6 限制条件](#16-限制条件)
- [2 实践案例：部署有状态的 Redis 集群](#2-实践案例部署有状态的-Redis-集群)
  - [2.1 部署StatefulSet](#21-部署StatefulSet)
    - [2.1.1 定义 Redis StatefulSet](#211-定义-Redis-StatefulSet)
    - [2.1.2 创建 Headless Service](#212-创建-Headless-Service)
    - [2.1.3 部署 StatefulSet](#213-部署-StatefulSet)
    - [2.1.4 观察StatefulSet 状态](#214-观察StatefulSet-状态)
    - [2.1.5 测试访问StatefulSet域名](#215-测试访问StatefulSet域名)
    - [2.1.6 验证存储](#216-验证存储)
  - [2.2 扩缩容StatefulSet](#22-扩缩容StatefulSet)
  - [2.3 更新StatefulSet](#23-更新StatefulSet)
    - [2.3.1 滚动更新策略](#231-滚动更新策略)
  - [2.4 版本回滚滚动](#24-版本回滚滚动)
    - [2.4.1 模拟更新StatefulSet出错](#241-模拟更新StatefulSet出错)
    - [2.4.2 检查StatefulSet的Rollout历史记录](#242-检查StatefulSet的Rollout历史记录)
    - [2.4.3 回滚到以前的修订](#243-回滚到以前的修订)
- [3 总结](#3-总结)
- [4 参考资料](#4-参考资料)

> ❤️ 摘要：本文详细介绍了Kubernetes中的StatefulSet概念和实际应用。文章通过比喻区分了有状态和无状态应用，并对比了StatefulSet与Deployment的不同之处。此外，还展示了StatefulSet的具体工作机制及其适用场景，包括数据库、分布式系统等。通过部署Redis集群的实际案例，演示了如何创建和管理StatefulSet，创建Headless Service、扩缩容及滚动更新等操作。通过本文，读者可以全面掌握StatefulSet的使用技巧，有效管理有状态应用。

![](image/statefulset_Y_ZxdYXh4R.png)

# 1 概述

对于刚接触Kubernetes的初学者来说，可能不清楚Deployment 和 StatefulSet两者的区别，因为它可以用来管理 Pod。这篇文章为您理清什么是StatefulSet，结合前文[《一文读懂Deployment以及实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142072026 "《一文读懂Deployment以及实践攻略》")让您可以适当选择和应用两者。

## 1.1 什么是有状态应用和无状态应用

在开始讲StatefulSet这个概念之前，我想先简单介绍一下什么是有状态应用和无状态应用。

**关于有状态应用和无状态应用比喻为两个“服务员”**。

有状态应用：记性好的服务员A
当你光顾一家餐厅，服务员A非常细心，会跟你打招呼，甚至记得您喜爱的座位和菜品。这就如**有状态应用**，它会保存用户的历史数据和会话状态。比如，购物车、登录信息等都保存在服务器端，每次你回来，服务员A都能准确无误地继续提供服务。

无状态应用：健忘的服务员B
现在你换了家餐厅，服务员B每次都要来问你要点什么。刚点完饮料，一会儿又过来问你是否要点餐。他完全不记得之前你说过什么，只记得你当下的需求。这就是**无状态应用**，每次请求都不依赖之前的会话状态，每个请求都是独立的，像是刚认识你一样。

## 1.2 什么是StatefulSet

在 Kubernetes 中，**StatefulSet** 是用于管理有状态应用的工作负载控制器。与无状态的 **Deployment** 相比，StatefulSet 适用于需要稳定标识、持久化存储以及有序启动和终止的应用场景，比如数据库、分布式缓存等。

## 1.3 StatefulSet 与 Deployment 的区别

StatefulSet 和 Deployment 在管理 Pod 的方式有以下主要区别：

| **特性**  | **StatefulSet**       | **Deployment**      |
| ------- | --------------------- | ------------------- |
| Pod 标识  | 每个 Pod 有唯一的标识，并且稳定不变。 | Pod 是随机的，重启后名字会改变。  |
| 存储卷     | 每个 Pod 拥有独立的、持久的存储卷。  | 默认情况下，Pod 共享同一个存储卷。 |
| 启动和终止顺序 | 确保按序启动和删除。            | 并行启动和删除，无先后顺序。      |
| 用途      | 适合需要稳定存储和标识的有状态应用。    | 适合无状态的分布式应用。        |

## 1.4 StatefulSet 的工作机制

StatefulSet 具备以下特性：

- **稳定的网络标识**：StatefulSet 为每个 Pod 分配一个唯一的标识符，如 `web-0`、`web-1`、`web-2`，这意味着每个 Pod 的名字、存储卷都会与它一一对应，不会因 Pod 重启而发生变化。同时每个 Pod 的 DNS 解析也是固定的，可以用 `podname-ordinal.servicename` 的形式来访问。
- **持久化存储**：每个 Pod 在创建时会独立分配和绑定一个或多个的 PersistentVolume，用于保证数据不会因 Pod 重启而丢失。
- **有序部署与扩缩**：StatefulSet 会按序（从 `0` 到 `n`）启动 Pods，并且扩展或缩减时也会按序进行。
- **优雅更新和终止**：StatefulSet 在更新和终止时，也是确保有序和数据完整性。

## 1.5 适用场景

StatefulSet 适用于那些需要有状态的数据应用场景，比如：

- **数据库**：如 MySQL、PostgreSQL 等，要求持久存储和稳定的网络标识。
- **分布式系统**：如 Cassandra、ZooKeeper，需要有序部署和数据一致性。
- **有状态应用**：如 Redis、Elasticsearch 等，需要在各个节点之间维护状态。

## 1.6 限制条件

StatefulSet的应用也有一些限制条件， 提前了解可以帮助我们在使用Kubernetes进行有状态应用的管理时，避免一些潜在的问题。

| 条件                                               | 说明                                                                                                                            | 案例                                                                                                                                          |
| ------------------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------- |
| 存储必须通过 PersistentVolume Provisioner 或管理员预先分配     | StatefulSet中的每个Pod需要持久化存储（PersistentVolume, PV），并且这种存储**要么由存储类（StorageClass）和PersistentVolume Provisioner自动创建，要么由管理员提前手动分配。** | 运行一个MySQL集群时，如果不为每个Pod配置PersistentVolume，当Pod重启或迁移到其他节点时，数据可能会丢失。因此，管理员需要为每个Pod预先配置一个PV，或者让Kubernetes根据存储类自动为每个Pod创建动态PV。                   |
| 删除或缩减StatefulSet不会删除相关的存储卷                       | 当删除StatefulSet或缩减其规模时，Kubernetes不会自动删除与这些Pod关联的PersistentVolume。                                                              | 运行一个Kafka集群时，每个Pod负责不同的分区日志存储。如果你意外缩减了StatefulSet规模或删除了它，Kafka集群的日志数据不会丢失，因为与Pod关联的存储卷（PersistentVolume）仍然存在，**在重新启动StatefulSet时可以恢复这些数据**。 |
| StatefulSet需要一个无头服务（Headless Service）来管理Pod的网络身份 | StatefulSet中的每个Pod都有一个唯一的、稳定的网络标识。这个标识是由无头服务（Headless Service）提供的，**用户需要手动创建此服务**，**以确保每个Pod都有一个稳定的DNS名称**，便于网络通信。            | 对于Cassandra数据库集群，无头服务确保每个Cassandra实例（Pod）都有一个唯一的网络标识，例如 cassandra-0.cassandra.default.svc.cluster.local，从而防止外部随意访问后端的Pod。                   |
| StatefulSet不保证Pod删除时的顺序                          | StatefulSet默认使用OrderedReady的Pod管理策略，在滚动更新时按照顺序进行Pod的升级。如果**某个Pod在更新时无法成功启动，整个滚动更新过程可能会停滞，需要手动干预来恢复状态**。                       | 运行一个ZooKeeper集群时，您可能希望在缩减规模时按顺序关闭Pod，先关闭zookeeper-2，再关闭zookeeper-1，最后关闭zookeeper-0，以确保集群在缩减期间不会失效。**通过手动缩减StatefulSet到0，你可以控制每个Pod按顺序终止。**  |
| 滚动更新时可能会进入一个需要手动干预的破坏状态                          | StatefulSet默认使用OrderedReady的Pod管理策略，在滚动更新时按照顺序进行Pod的升级。如果**某个Pod在更新时无法成功启动，整个滚动更新过程可能会停滞，需要手动干预来恢复状态**。                       | 运行一个Elasticsearch集群时，如果在滚动更新期间某个Pod因为配置问题无法启动，整个更新过程将被卡住，导致集群无法继续运行。此时，**你需要手动检查并删除失败的Pod或修复问题，才能继续滚动更新。**                                  |

# 2 实践案例：部署有状态的 Redis 集群

我们将通过一个简单的例子来演示如何使用 StatefulSet 来部署一个有状态的 Redis 集群。

![](image/image_6TEjuROcQj.png)

## 2.1 部署StatefulSet

### 2.1.1 定义 Redis StatefulSet

> 首先，我们定义一个 Redis StatefulSet的yaml，确保每个 Redis 节点有自己独立的存储，并且能够在重启后恢复。

编辑redis-statefulset.yaml

```yaml 
apiVersion: apps/v1
kind: StatefulSet
metadata:
  # 定义StatefulSet名称
  name: redis
spec:
  # 名称与无头service关联
  serviceName: "redis"
  # 定义副本数，默认是1
  replicas: 3
  # 可选项，检查pod是否就绪的时间间隔，默认是0
  minReadySeconds: 20
  # label与pod模板关联
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      # label与StatefulSet关联
      labels:
        app: redis
    spec:
      # 定义pod
      containers:
      - name: redis
        # 指向私有仓库的镜像
        image: harbor.zx/hcie/redis:7.2.4
        ports:
        - containerPort: 6379
        # 挂载数据卷到/data目录
        volumeMounts:
        - name: redis-storage
          mountPath: /data
  # 定义卷模板
  volumeClaimTemplates:
  - metadata:
      name: redis-storage
    spec:
      # 使用nfs作为StorageClass，自动创建动态PV
      storageClassName: "nfs-class"
      # 访问模式，对于生产使用，建议使用ReadWriteOncePod访问模式
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 1Gi
```


### 2.1.2 创建 Headless Service

> 上文介绍了StatefulSet是需要依赖一个专门的 **Headless Service** 来管理网络标识并对外提供服务。我们需要先创建一个 Headless Service。

编辑redis-service.yaml

```yaml 
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  clusterIP: None
  selector:
    app: redis
  ports:
  - port: 6379
    name: redis
```


或者也可以将两个yaml合并redis-sts.yaml，完整配置如下：

```bash 
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  clusterIP: None
  selector:
    app: redis
  ports:
  - port: 6379
    name: redis

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
spec:
  serviceName: "redis"
  replicas: 3
  minReadySeconds: 20
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis-test
        image: harbor.zx/hcie/redis:7.2.4
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-storage
          mountPath: /data
  volumeClaimTemplates:
  - metadata:
      name: redis-storage
    spec:
      storageClassName: "nfs-class"
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 1Gi

```


### 2.1.3 部署 StatefulSet

执行以下命令将定义的 StatefulSet 和 Headless Service 部署到 Kubernetes 集群中：

```bash 
kubectl apply -f redis-service.yaml
kubectl apply -f redis-statefulset.yaml
```


或者

```bash 
kubectl apply -f redis-sts.yaml
```


### 2.1.4 观察StatefulSet 状态

你可以通过以下命令来查看 StatefulSet 的 Pods 状态：

```bash 
kubectl get pods -l app=redis
```


输出将显示类似以下的结果

```yaml 
redis-0   1/1     Running   0          39m   app=redis,apps.kubernetes.io/pod-index=0,controller-revision-hash=redis-59bb8c87b9,statefulset.kubernetes.io/pod-name=redis-0
redis-1   1/1     Running   0          39m   app=redis,apps.kubernetes.io/pod-index=1,controller-revision-hash=redis-59bb8c87b9,statefulset.kubernetes.io/pod-name=redis-1
redis-2   1/1     Running   0          38m   app=redis,apps.kubernetes.io/pod-index=2,controller-revision-hash=redis-59bb8c87b9,statefulset.kubernetes.io/pod-name=redis-2

```


> ❔ 说明：每个 Pod创建时`statefulset.kubernetes.io/pod-name=redis-x`会生成并指定Pod名字。

通过以下命令来查看 StatefulSet 的Svc状态：

```bash 
kubectl get svc redis

```


输出将显示类似以下的结果

```text 
NAME    TYPE        CLUSTER-IP   EXTERNAL-IP   PORT(S)    AGE
redis   ClusterIP   None         <none>        6379/TCP   3h40m

```


### 2.1.5 测试访问StatefulSet域名

运行一个dnstools测试pod

```bash 
 kubectl run -it --rm --restart=Never --image=harbor.zx/hcie/dnstools:latest dnstools

```


对redis的域名镜像解析

```bash 
# 解析redis svc的域名
dnstools# nslookup redis.default.svc.cluster.local
Server:         ************
Address:        ************#53

Non-authoritative answer:
Name:   redis.default.svc.cluster.local.default.svc.cluster.com
Address: ***********
# 解析redis-0的域名
dnstools# nslookup redis-0.redis.default.svc.cluster.local
Server:         ************
Address:        ************#53

Name:   redis-0.redis.default.svc.cluster.local
Address: **************
# 解析redis-1的域名
dnstools# nslookup redis-1.redis.default.svc.cluster.local
Server:         ************
Address:        ************#53

Name:   redis-1.redis.default.svc.cluster.local
Address: **************
# 解析redis-2的域名
dnstools# nslookup redis-2.redis.default.svc.cluster.local
Server:         ************
Address:        ************#53

Name:   redis-2.redis.default.svc.cluster.local
Address: *************

```


> ❔ 说明：`redis.default.svc.cluster.local` 是一个典型的内部服务域名（FQDN, Fully Qualified Domain Name），用于访问集群中的服务。这个域名结构遵循一定的命名规则，每个部分都有特定的含义。让我逐个解释：

- **`redis-0`**:  这是Pod的名称。
- **`redis`**：这是**服务名称**，对应yaml定义的服务（`Service`）资源的名称。
- **`default`**：这是服务所属的**命名空间（Namespace）**。在 Kubernetes 中，命名空间用于将资源隔离，默认命名空间是 `default`。
- **`svc`**：表示这个域名是一个**服务（Service）** 资源。Kubernetes 的 DNS 系统会为每个服务创建一个类似于`*.svc` 的记录。
- **`cluster`**：这是指代**集群域名（Cluster Domain）**。`cluster.local` 是 Kubernetes 中的默认集群域名，表示这是集群内的 DNS 名称。
- **`local`**：这是**顶级域名（TLD, Top-Level Domain）**，通常是 `local`，表示该域名仅用于集群内部，不会对外暴露，也无法通过外部 DNS 系统解析。

> ⚠️ 注意：有时访问Pod或者Service域名的时候会出现一段时间的报错

```bash 
/ # nslookup redis-0.redis.default.svc.cluster.local
Server:         ************
Address:        ************:53


Server:         ************
Address:        ************#53

** server can't find redis-0.redis.default.svc.cluster.local: NXDOMAIN


```


- 这可能是DNS负缓存导致，可以看[《【Kubernetes知识点】为什么DNS解析会超时？》](https://stevenzeng.blog.csdn.net/article/details/142187721 "《【Kubernetes知识点】为什么DNS解析会超时？》")一文。

### 2.1.6 验证存储

通过 StatefulSet 提供的 VolumeClaimTemplates，每个 Pod 都会拥有自己的持久化存储卷。你可以通过以下命令查看这些卷的状态：

```markdown 
# 查看pvc
kubectl get pvc -l app=redis

# 查看pv
kubectl get pv |grep -e redis -e NAME




```


可以看到为每个 Pod 创建的持久化存储PVC、PV：

```yaml 
NAME                    STATUS   VOLUME                                     CAPACITY   ACCESS MODES   STORAGECLASS   VOLUMEATTRIBUTESCLASS   AGE
redis-storage-redis-0   Bound    pvc-1e2027aa-05c7-4ed9-a2b3-541c705f95cb   1Gi        RWO            nfs-class      <unset>                 3h57m
redis-storage-redis-1   Bound    pvc-2eebbdd6-6222-4fc1-8f18-285b4f5b2b75   1Gi        RWO            nfs-class      <unset>                 3h56m
redis-storage-redis-2   Bound    pvc-0863b6b0-83f4-4b25-91ff-2cec14ec21fb   1Gi        RWO            nfs-class      <unset>                 3h55m

```


```bash 
NAME                                       CAPACITY   ACCESS MODES   RECLAIM POLICY   STATUS   CLAIM                           STORAGECLASS   VOLUMEATTRIBUTESCLASS   REASON   AGE
pvc-0863b6b0-83f4-4b25-91ff-2cec14ec21fb   1Gi        RWO            Delete           Bound    default/redis-storage-redis-2   nfs-class      <unset>                          3h57m
pvc-1e2027aa-05c7-4ed9-a2b3-541c705f95cb   1Gi        RWO            Delete           Bound    default/redis-storage-redis-0   nfs-class      <unset>                          3h59m
pvc-2eebbdd6-6222-4fc1-8f18-285b4f5b2b75   1Gi        RWO            Delete           Bound    default/redis-storage-redis-1   nfs-class      <unset>                          3h58m
```


## 2.2 扩缩容StatefulSet

> ❔ 说明：StatefulSet在扩容时，新增加的 Pod 名称会遵循序号递增规则，如：redis-3、redis-4。并且每个 Pod 的存储是独立的，即使扩容也不会影响已经存在的存储卷。

**接下来将一个Redis StatefulSet 扩容并观察其变化。**

扩容 StatefulSet： 修改 StatefulSet 的 `replicas` 值，扩展副本数。例如，增加到 `5` 个副本：

```bash 
kubectl scale statefulset redis --replicas=5

```


观察扩容进度，执行以下命令：

```bash 
kubectl rollout status statefulset redis
```


输出结果如下：

```bash 
Waiting for 1 pods to be ready...
Waiting for 1 pods to be ready...
Waiting for 1 pods to be ready...
partitioned roll out complete: 5 new pods have been updated...

```


验证扩容结果： 查看新的 Pod 是否已经启动：

```bash 
kubectl get pods -l app=redis

```


输出结果如下：

```bash 
NAME      READY   STATUS    RESTARTS   AGE
redis-0   1/1     Running   0          4h21m
redis-1   1/1     Running   0          4h20m
redis-2   1/1     Running   0          4h19m
redis-3   1/1     Running   0          2m14s
redis-4   1/1     Running   0          94s

```


- 您会看到 `redis-3` 和 `redis-4` 新创建的 Pod，且依次启动。

缩容 StatefulSet： 修改 StatefulSet 的 `replicas` 值，扩展副本数。例如，增加到 `1` 个副本：

```bash 
kubectl scale statefulset redis --replicas=1
```


验证缩容结果： 查看 Pod 是否已经被删：

```bash 
kubectl get pods -l app=redis -w

```


输出结果如下：

```bash 
[root@k8s-master1 harbor.zx]# kubectl get pod -l app=redis -w
NAME      READY   STATUS    RESTARTS   AGE
redis-0   1/1     Running   0          4h26m
redis-1   1/1     Running   0          4h25m
redis-2   1/1     Running   0          4h24m
redis-3   1/1     Running   0          7m50s
redis-4   1/1     Running   0          7m10s
redis-4   1/1     Terminating   0          7m14s
redis-4   1/1     Terminating   0          7m15s
redis-4   0/1     Terminating   0          7m15s
redis-4   0/1     Terminating   0          7m16s
redis-4   0/1     Terminating   0          7m16s
redis-4   0/1     Terminating   0          7m16s
redis-3   1/1     Terminating   0          7m56s
redis-3   1/1     Terminating   0          7m56s
redis-3   0/1     Terminating   0          7m57s
redis-3   0/1     Terminating   0          7m57s
redis-3   0/1     Terminating   0          7m57s
redis-3   0/1     Terminating   0          7m58s
redis-2   1/1     Terminating   0          4h25m
redis-2   1/1     Terminating   0          4h25m
redis-2   0/1     Terminating   0          4h25m
redis-2   0/1     Terminating   0          4h25m
redis-2   0/1     Terminating   0          4h25m
redis-2   0/1     Terminating   0          4h25m
redis-1   1/1     Terminating   0          4h26m
redis-1   1/1     Terminating   0          4h26m
redis-1   0/1     Terminating   0          4h26m
redis-1   0/1     Terminating   0          4h26m
redis-1   0/1     Terminating   0          4h26m
redis-1   0/1     Terminating   0          4h26m

```


- 您会看到 只剩下`redis-0` Pod。

> ❔ 说明：

- 当执行扩展操作时，会按照从 {0..N-1} 的顺序依次创建 Pod，并且会监听前一个pod是否处于 Running 和 Ready 状态，再进行下一步操作。
- 当执行缩容操作时，它们会以相反的顺序终止，从 {N-1..0} 开始，并且会监听后一个pod是否处于完全关闭状态，再进行下一步操作。

> ⚠️ 注意：**强烈不建议将**\*\*`pod.Spec.TerminationGracePeriodSeconds`设置为 0，执行强制删除操作，否则会导致集群数据混乱 \*\*。

> ⚠️ 注意： **如果非常情况需要强行删除，请执行以下命令**：

```bash 
kubectl delete pods <pod> --grace-period=0 --force
```


## 2.3 更新StatefulSet

**接下来更新 Redis StatefulSet 到一个新的镜像版本，观察更新的过程。**

将redis StatefulSet扩容到3个

```bash 
kubectl scale statefulset redis --replicas=3
```


编辑 StatefulSet： 更新 StatefulSet 的镜像版本（例如，将 Redis 更新到 7.2.5）：

```bash 
kubectl set image statefulset/redis redis-test=harbor.zx/hcie/redis:7.2.5

```


观察更新进度

```bash 
[root@k8s-master1 hcie]# kubectl rollout status sts redis
Waiting for partitioned roll out to finish: 1 out of 3 new pods have been updated...
Waiting for 1 pods to be ready...
Waiting for 1 pods to be ready...
Waiting for 1 pods to be ready...
Waiting for partitioned roll out to finish: 2 out of 3 new pods have been updated...
Waiting for partitioned roll out to finish: 2 out of 3 new pods have been updated...
Waiting for 1 pods to be ready...
Waiting for 1 pods to be ready...
Waiting for 1 pods to be ready...
partitioned roll out complete: 3 new pods have been updated...

```


查看pod的详细信息

```bash 
[root@k8s-master1 hcie]# kubectl get pod redis-0 -ojsonpath="{.spec.containers[*].image}"
```


镜像成功更新

```bash 
harbor.zx/hcie/redis:7.2.5
```


> ❔ 说明：当执行更新操作时，会以相反的顺序，从 {N-1..0} 开始，并且会监听前一个pod是否处于 Running 和 Ready 状态，再进行下一步操作。

### 2.3.1 滚动更新策略

> ❓ 思考：StatefulSet是如何控制滚动更新的，能否自定义调整滚动更新策略？

**这是可以的**，StatefulSet对滚动更新策略规定了相关参数，参数如下：

| 参数                                                  | 说明                                                                                                                                              | 可能值                                             |
| --------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------- |
| `.spec.updateStrategy.type`                         | 指定StatefulSet的更新策略                                                                                                                              | 1）RollingUpdate：自动更新（默认值）\<br>2) OnDelete: 手动更新 |
| `.spec.minReadySeconds`                             | 控制平面会在 Pod 准备就绪后继续等待该时间，然后再继续。如果应用启动还要加载第三方组件等， 建议设置该值，或者使用就绪探针。                                                                                | 默认是0s                                           |
| `.spec.updateStrategy.rollingUpdate.partition`      | 如果指定了分区，则当更新 StatefulSet 的.spec.template时，**所有序数大于或等于该分区的 Pod 都将被更新。所有序号小于分区的 Pod 都不会被更新，并且即使被删除，也会以之前的版本重新创建**。如果您想要暂存更新、推出金丝雀或执行分阶段推出，它们会很有用。 | 该字段适用于0到replicas - 1，默认是0                       |
| `.spec.updateStrategy.rollingUpdate.maxUnavailable` | 指定以下参数来控制更新期间不可用的最大 Pod 数量，可以是数值或者百分数                                                                                                           | 该字段适用于0到replicas - 1范围内的所有 Pod，默认是1             |

> ❓ 思考：如果是关键业务或依赖链较复杂的应用，既希望更新应用，又希望对更新过程有完全控制的场景，那应该实现？

如果是对更新敏感的场景，我们可以采用以下几个策略：

1. 设置Pod的探针，如初始化探针、就绪探针，防止Pod未正在业务就绪就投入生产；**（后续的文章中详细说明）**
2. 设置StatefulSet的更新策略为OnDelete，当 StatefulSet 的`.spec.updateStrategy.type`设置为`OnDelete`时，StatefulSet 控制器不会自动更新 StatefulSet 中的 Pod。用户必须手动删除 Pod ,控制器会对 按StatefulSet 的`.spec.template`更新的内容创建新 Pod。

## 2.4 版本回滚滚动

StatefulSet也是支持版本回滚，可以了解[《一文读懂Deployment以及实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142072026 "《一文读懂Deployment以及实践攻略》")，对比两者的不同

### 2.4.1 模拟更新StatefulSet出错

例如，**当新的StatefulSet 出现崩溃（更新后一直未变为“正在运行”和“就绪”状态）时，这时StatefulSet会直接停止更新并等待**。这时你可以查看报错信息，并且查看修订历史版本，以便回滚到正常配置的修订历史版本。

假设您在更新部署时，将映像名称拼写错误，设置为`redis:7.26 `：

```bash 
kubectl set image statefulset/redis redis-test=harbor.zx/hcie/redis:7.26

```


查询rollout状态，会发现会被卡住：

```bash 
[root@k8s-master1 hcie]# kubectl rollout status sts redis
Waiting for 1 pods to be ready...


```


查看pods状态，可以直观看到是镜像拉取出错

```bash 
[root@k8s-master1 harbor.zx]# kubectl get pod -l app=redis -w
NAME                                               READY   STATUS         RESTARTS      AGE
redis-0                                            1/1     Running        0             51m
redis-1                                            1/1     Running        0             51m
redis-2                                            0/1     ErrImagePull   0             15s
redis-2                                            0/1     ImagePullBackOff   0             18s
redis-2                                            0/1     ErrImagePull       0             32s

```


### 2.4.2 检查StatefulSet的Rollout历史记录

首先，检查此 StatefulSet 的修订(revisions),执行以下命令：

```bash 
[root@k8s-master1 hcie]# kubectl rollout history statefulset/redis
statefulset.apps/redis
REVISION  CHANGE-CAUSE
1         <none>
2         <none>
3         <none>


```


> ❔ 说明：

- 当触发statefulset的rollout时，会创建statefulset的修订版本。这意味着当且仅当 statefulset的 Pod 模板 ( `.spec.template` ) 发生更改时才会创建新修订版本，例如，如果您更新模板的标签或容器映像。
- `CHANGE-CAUSE`在创建时从statefulset的注释`kubernetes.io/change-cause`复制到其修订版本。您可以通过以下方式指定`CHANGE-CAUSE`消息：

```bash 
kubectl annotate statefulset/redis kubernetes.io/change-cause="image updated to 7.2.6"
```


- 或者直接修改statefulset的注释

```bash 
kubectl edit statefulset/redis
---
apiVersion: apps/v1
kind: statefulset
metadata:
  annotations:
    # 更新时新增注释
    kubernetes.io/change-cause: image updated to 7.2.6

```


然后，要查看上一个修订的详细信息，执行以下命令：

```bash 
kubectl rollout history statefulset/redis --revision=2

```


输出如下：

```bash 
statefulset.apps/redis with revision #2
Pod Template:
  Labels:       app=redis
  Containers:
   redis-test:
    Image:      harbor.zx/hcie/redis:7.2.5
    Port:       6379/TCP
    Host Port:  0/TCP
    Environment:        <none>
    Mounts:
      /data from redis-storage (rw)
  Volumes:      <none>

```


### 2.4.3 回滚到以前的修订

按照下面给出的步骤将 statefulset 从当前版本回滚到之前的版本，即版本 2。

现在可以撤消当前的rollout操作：

```bash 
kubectl rollout undo statefulset/redis

```


输出如下：

```bash 
statefulset.apps/redis rolled back

```


或者，直接指定历史版本回滚：

```bash 
kubectl rollout undo deployment/nginx-deployment --to-revision=2

```


查看pod的状态

```bash 
[root@k8s-master1 harbor.zx]# kubectl get pod -l app=redis-2
NAME                                               READY   STATUS             RESTARTS      
...
redis-2                                            0/1     ImagePullBackOff   0  
```


> ❓ 思考：版本已经回滚了，但是redis-2还是没有重新起来， 这是为什么呢？

当pod模板恢复正常配置后，您还必须手动删除 StatefulSet 已使用错误配置运行的所有 Pod。然后 StatefulSet 将开始使用恢复的模板重新创建 Pod。

手动删除redis-2

```bash 
[root@k8s-master1 harbor.zx]# kubectl delete pod redis-2
pod "redis-2" deleted

```


观察pod状态

```bash 
[root@k8s-master1 harbor.zx]# kubectl get pod -l app=redis
NAME      READY   STATUS    RESTARTS   AGE
redis-0   1/1     Running   0          66m
redis-1   1/1     Running   0          67m
redis-2   1/1     Running   0          23s

```


- 现在Pod全部起来了。

# 3 总结

StatefulSet 是 Kubernetes 中为有状态应用提供的一种强大的工作负载控制器。它通过稳定的网络标识、持久化存储和有序操作，确保在处理数据库、分布式系统等需要保持状态的场景中能够高效运作。通过本篇博客，你可以全面了解StatefulSet，轻松地部署和管理有状态应用，实现应用的高可用性和持久性管理。

# 4 参考资料

\[1] [StatefulSets](https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#partitions "StatefulSets")

\[2] [Scale a StatefulSet](https://kubernetes.io/docs/tasks/run-application/scale-stateful-set/ "Scale a StatefulSet")

\[3] [Force Delete StatefulSet Pods](https://kubernetes.io/docs/tasks/run-application/force-delete-stateful-set-pod/ "Force Delete StatefulSet Pods")
