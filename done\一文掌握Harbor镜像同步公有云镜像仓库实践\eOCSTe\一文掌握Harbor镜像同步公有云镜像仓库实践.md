# 一文掌握Harbor镜像同步公有云镜像仓库实践

## 目录

- [1 引言](#1-引言)
- [2 概念](#2-概念)
  - [2.1 Harbor](#21-Harbor)
  - [2.2 阿里云的镜像仓库ACR](#22-阿里云的镜像仓库ACR)
  - [2.3 华为云的镜像仓库SWR](#23-华为云的镜像仓库SWR)
  - [2.4 Harbor复制管理同步镜像](#24-Harbor复制管理同步镜像)
    - [2.4.1 复制管理的工作原理](#241-复制管理的工作原理)
  - [2.5 Harbor同步镜像到公有云镜像仓库的优势](#25-Harbor同步镜像到公有云镜像仓库的优势)
- [3 实验：通过Harbor 将容器镜像同步到公有云镜像仓库](#3-实验通过Harbor-将容器镜像同步到公有云镜像仓库)
  - [3.1  前置条件](#31--前置条件)
  - [3.2 上传镜像到阿里云镜像仓库](#32-上传镜像到阿里云镜像仓库)
    - [3.2.1 在阿里云创建镜像仓库](#321-在阿里云创建镜像仓库)
    - [3.2.2 创建阿里云目标镜像仓库](#322-创建阿里云目标镜像仓库)
    - [3.2.3 配置复制策略](#323-配置复制策略)
    - [3.2.4 将镜像推送到阿里云镜像仓库](#324-将镜像推送到阿里云镜像仓库)
  - [3.3 上传镜像到华为云镜像仓库](#33-上传镜像到华为云镜像仓库)
    - [3.3.1 在华为云创建镜像仓库](#331-在华为云创建镜像仓库)
    - [3.3.2 在Harbor创建华为云目标镜像仓库](#332-在Harbor创建华为云目标镜像仓库)
    - [3.3.3 创建目标镜像仓库](#333-创建目标镜像仓库)
    - [3.3.4 配置复制策略](#334-配置复制策略)
    - [3.3.5 将镜像推送到华为云镜像仓库](#335-将镜像推送到华为云镜像仓库)
- [4 总结](#4-总结)
- [参考文献](#参考文献)

❤️**摘要：** 在云原生应用的开发和部署过程中，镜像管理是关键的一环。为了保证高效、安全的镜像分发到多云环境，企业往往使用多个公有云的镜像服务建设自有的镜像仓库来存储业务镜像。在本文中，我们将介绍如何将容器镜像从**Harbor** 推送到 **阿里云镜像仓库**，实现镜像的同步与加速分发。

***

💯 本文关联好文：

- [《一文读懂Harbor以及部署实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142024722 "《一文读懂Harbor以及部署实践攻略》")
- [《一文掌握Containerd配置Harbor私有仓库》](https://stevenzeng.blog.csdn.net/article/details/142045913 "《一文掌握Containerd配置Harbor私有仓库》")
- [《一文掌握Harbor的双向认证实践》](https://stevenzeng.blog.csdn.net/article/details/142064229 "《一文掌握Harbor的双向认证实践》")
- [《【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践》](https://stevenzeng.blog.csdn.net/article/details/142470707 "《【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践》")
- [《【云原生安全篇】一文掌握Harbor集成Trivy应用实践》](https://stevenzeng.blog.csdn.net/article/details/142255573 "《【云原生安全篇】一文掌握Harbor集成Trivy应用实践》")
- 《【云原生安全篇】Cosign助力Harbor验证镜像实践》

# 1 引言

随着容器技术的普及，镜像仓库成为了 DevOps 流水线中的重要组成部分。在实际生产中，企业需要将镜像推送到多个公有云上（如阿里云镜像仓库），以利用其全球加速和高可用性。但通常出于安全考虑或网络限制，开发环境的服务器一般会做网络隔离，无法直接访问互联网。为了在这种环境下保持开发流程的顺畅，我们可以通过私有镜像仓库Harbor，先将镜像上传到内部的Harbor，再通过Harbor将镜像自动同步到其他云厂商的镜像仓库。

# 2 概念

## 2.1 Harbor

![](image/image__5uIkO_-Ch.png)

Harbor 是一个企业级的云原生容器镜像仓库，由 VMware 主导开发并贡献给 Cloud Native Computing Foundation (CNCF)。它通过为 Docker 镜像提供安全、高效的管理能力，帮助企业简化容器应用程序的交付流程。相比于传统的 Docker Registry，Harbor 提供了更多的企业级特性，如先前介绍的安全扫描、镜像签名，还有接下来介绍的镜像复制。

## 2.2 阿里云的镜像仓库ACR

![](image/image_SLRypouEbM.png)

阿里云容器镜像服务ACR（Alibaba Cloud Container Registry）是面向容器镜像、Helm Chart等符合OCI标准云原生制品安全托管及高效分发平台。ACR企业版支持全球同步加速、大规模和大镜像分发加速、多代码源构建加速等全链路加速能力，帮助企业降低交付复杂度，打造云原生应用一站式解决方案。

## 2.3 华为云的镜像仓库SWR

![](image/image_kdXpD4l18V.png)

容器镜像服务（SoftWare Repository for Container，简称SWR）是一种支持镜像全生命周期管理的服务， 提供简单易用、安全可靠的镜像管理功能，帮助您快速部署容器化服务。

## 2.4 Harbor复制管理同步镜像

镜像同步是一种自动化的镜像传输机制，允许用户在不同的镜像仓库之间定期同步容器镜像。它可以帮助团队在多云或混合云环境下保持不同仓库中的镜像一致性，避免手动推拉镜像的繁琐步骤。

### 2.4.1 复制管理的工作原理

Harbor的复制管理功能通过配置**复制策略**（Replication Policies），将镜像从源仓库复制到目标仓库。复制策略包含以下几个部分：

- **源仓库**：你可以选择Harbor本地的某个项目，或外部镜像仓库作为源仓库。
- **目标仓库**：目标仓库可以是另一个Harbor实例或外部公共镜像仓库，例如Docker Hub、阿里云镜像仓库等。
- **复制模式**：Harbor提供单向复制（Push）和双向同步（Pull & Push）两种复制模式。单向复制时，镜像从源仓库推送到目标仓库；双向同步时，镜像可以在源和目标仓库之间互相推拉。
- **过滤器**：你可以根据镜像名称、标签等设置过滤器，选择性复制部分镜像。例如，可以设置仅复制特定标签的镜像，或只复制某个项目中的镜像。
- **触发模式**：Harbor支持多种触发模式，常见的有“手动触发”、“事件驱动”（如镜像推送事件）、“定时任务”等，用户可以根据需要选择触发方式。
- **认证与访问控制**：在进行复制时，Harbor需要能够访问目标仓库。你需要为Harbor配置访问目标仓库的凭证，如用户名和密码或访问令牌。

## 2.5 Harbor同步镜像到公有云镜像仓库的优势

![](image/image_8d1m_5MxUf.png)

如上图，在多云环境中，企业可能需要将应用镜像同时部署到多个云平台。通过Harbor的复制功能，企业可以在不同的云服务商（如AWS、阿里云、Azure等）之间自动同步镜像，这样做的优势包括如下：

- **统一管理镜像仓库入口**：Harbor作为一个中央镜像仓库，客户端只需与Harbor交互，所有镜像的管理、上传和下载都通过一个统一的接口进行。同时做到统一认证和授权，减少运维开销。
- **镜像签名服务**：Harbor支持Cosign和Notary进行镜像签名，通过对镜像进行数字签名，可以保证镜像的完整性和可溯源性，可以防止未经过签名的镜像被部署到生产环境，提升镜像使用过程中的安全性。
- **自动化和高效管理**：Harbor支持镜像复制规则的配置，可以将镜像自动同步到多个目标仓库，这种方式不仅节省了手动操作的时间，还可以确保所有镜像版本在各个云环境中保持一致。还可以将镜像同步到公有云镜像仓库就近区域，确保本地化的快速拉取，减少网络延迟和传输瓶颈。
- **提高镜像拉取速度和优化带宽利用**：Harbor可以作为内部的缓存层，减少直接从公有云镜像仓库下载镜像的频次，节省带宽和减少延迟。
- **统一安全控制和漏洞扫描**：Harbor内置了镜像安全漏洞扫描工具，比如**Trivy**，能够对存储在仓库中的镜像进行自动扫描，发现已知的安全漏洞，生成报告，供开发和运维团队及时修复漏洞，减少镜像发布的风险。
- **审计与合规性管理**：Harbor提供详细的审计日志功能，允许对镜像的所有操作进行记录和追踪
- **多租户与访问控制**：Harbor支持**多租户管理**，可以为不同的项目设置访问权限，保障各团队或应用的镜像管理安全。
- **降低云服务依赖，增加灵活性**：通过Harbor统一管理镜像，可以避免企业直接依赖某个特定的公有云供应商。

# 3 实验：通过\*\*Harbor \*\*将容器镜像同步到公有云镜像仓库

实验中，我将harbor 私有仓库的镜像同步到阿里云和华为云的镜像仓库中，并检查在公有云的镜像仓库是否能看到新推送的镜像。

## 3.1  前置条件

在开始之前，确保以下条件已准备好：

- **Harbor 已安装并配置**：用于管理本地镜像。
- **阿里云容器镜像服务（ACR）账号**：用于云端存储镜像。
- **华为云容器镜像服务（SWR）账号**：用于云端存储镜像。

***

## 3.2 上传镜像到阿里云镜像仓库

### 3.2.1 在阿里云创建镜像仓库

在服务台搜索“ACR”，选择“ 容器镜像服务”。

![](image/image_P590xd5QgQ.png)

以下用个人版做实验。

![](image/image_al7EdSdsze.png)

创建命名空间，并做相关配置。

![](image/image_dslDmPcgfb.png)

> ❔ 参数说明：

| **参数**​     | **说明**​                                          |
| ----------- | ------------------------------------------------ |
| **命名空间**​   | 填入Harbor中需要同步的项目名称。例如test-project。               |
| **自动创建仓库**​ | 选择\*\*开启\*\*。若选择\*\*关闭\*\*，在同步镜像前需要在ACR中创建相应的仓库。 |
| **默认仓库类型**​ | 自定义。建议选择\*\*私有\*\*。                              |

点击“访问凭证”，创建固定密码，并记录客户端登录命令

![](image/image_h-Ae-pY0cA.png)

### 3.2.2 创建阿里云目标镜像仓库

登录到Harbor的管理后台，进行一下操作。

1. 在左侧导航栏选择**系统管理** > **仓库管理**。
2. 在**仓库管理**页面，单击**新建目标**。

![](image/image_qD3MxuNG9D.png)

在**新建目标**对话框，填写参数配置。

| **参数**​    | **说明**​                    |
| ---------- | -------------------------- |
| **提供者**​   | 选择Docker Registry。         |
| **目标名**​   | 自定义。                       |
| **描述**​    | 自定义。                       |
| **目标URL**​ | 填写ACR实例目标仓库域名地址，确保访问控制已开启。 |
| **访问ID**​  | 仓库登录名，阿里云用户账号名。            |
| **访问密码**​  | 仓库登录密码。                    |

![](image/image_CGegLCV5NI.png)

### 3.2.3 配置复制策略

1. 在左侧导航栏选择**系统管理** > **复制管理**。
2. 在**复制管理**页面，单击**新建规则**。

![](image/image_OiI0ekDzVL.png)

在**新建规则**对话框，填写参数。单击**保存**。

| **参数**​     | **说明**​                                                                                                |
| ----------- | ------------------------------------------------------------------------------------------------------ |
| **名称**​     | 自定义。                                                                                                   |
| **描述**​     | 自定义。                                                                                                   |
| **复制模式**​   | 选择\*\*Push-based\*\*。                                                                                  |
| **源资源过滤器**​ | 用于过滤需要同步的资源，可依据Harbor界面的提示自行填写，默认为全部资源。                                                                |
| **目标仓库**​   | 选择上一步创建的目标。                                                                                            |
| **目标**​     | 名称空间填写目标ACR上的命名空间；仓库扁平化用以在复制镜像时减少仓库的层级结构，推荐选择\*\*替换1级\*\*，例如：\`harbor-project/nginx -> acr-ns/nginx\`。 |
| **触发模式**​   | 自定义。建议选择\*\*事件驱动\*\*来同步Harbor上的镜像改动。                                                                   |
| **带宽**​     | 限制同步时的最大网络带宽。默认为 **-1**，表示无限制。                                                                         |

![](image/image_G7FyNCGb8C.png)

### 3.2.4 将镜像推送到阿里云镜像仓库

在**复制管理**页面，选择上一步骤中创建的规则，单击**复制**。可以手动同步存量的镜像到ACR实例。当相应的复制任务转化为**Succeeded**后，同步任务运行成功。

![](image/image_sDRKFcW3Pi.png)

ACR服务可以看到新推送的busybox镜像。

![](image/image_2jT65Fn9M_.png)

## 3.3 上传镜像到华为云镜像仓库

### 3.3.1 在华为云创建镜像仓库

登录华为云，在服务台搜索SWR，选择“容器镜像服务”

![](image/image_D84BXVS-mt.png)

创建一个组织，类似Harbor 的项目

![](image/image_m--Ispkpde.png)

创建长期有效密钥，后面创建目标端有用。

![](image/image_B8m2dvj603.png)

![](image/image_X1gR2_Ct2_.png)

### 3.3.2 在Harbor创建华为云目标镜像仓库

登录到Harbor的管理后台，进行一下操作。

### 3.3.3 创建目标镜像仓库

1. 在左侧导航栏选择**系统管理** > **仓库管理**。
2. 在**仓库管理**页面，单击**新建目标**。

![](image/image_qD3MxuNG9D.png)

在**新建目标**对话框，填写参数配置。

| **参数**​    | **说明**​                                                                                 |
| ---------- | --------------------------------------------------------------------------------------- |
| **提供者**​   | 必须选择“Huawei SWR”。                                                                       |
| **目标名**​   | 自定义。                                                                                    |
| **描述**​    | 自定义。                                                                                    |
| **目标URL**​ | 填写SWR实例目标仓库域名地址。镜像仓库地址获取方法：登录容器镜像服务控制台，进入“我的镜像”，单击“客户端上传”，在弹出的页面即可查看SWR当前Region的镜像仓库地址。 |
| **访问ID**​  | 仓库登录名，需要生成长期有效密钥。                                                                       |
| **访问密码**​  | 仓库登录密码，需要生成长期有效密钥。                                                                      |

测试连接是否成功。

![](image/image_ZA3bSFZaSB.png)

### 3.3.4 配置复制策略

1. 在左侧导航栏选择**系统管理** > **复制管理**。
2. 在**复制管理**页面，单击**新建规则**。

![](image/image_OiI0ekDzVL.png)

在**新建规则**对话框，填写参数。单击**保存**。

| **参数**​     | **说明**​                                                    |
| ----------- | ---------------------------------------------------------- |
| **名称**​     | 自定义。                                                       |
| **描述**​     | 自定义。                                                       |
| **复制模式**​   | 选择\*\*Push-based\*\*。                                      |
| **源资源过滤器**​ | 用于过滤需要同步的资源，可依据Harbor界面的提示自行填写，默认为全部资源。                    |
| **目标仓库**​   | 选择上一步创建的目标。                                                |
| **目标**​     | 名称空间填写目标SWR上的命名空间；仓库扁平化用以在复制镜像时减少仓库的层级结构，推荐选择\*\*替换所有级\*\* |
| **触发模式**​   | 自定义。建议选择\*\*事件驱动\*\*来同步Harbor上的镜像改动。                       |
| **带宽**​     | 限制同步时的最大网络带宽。默认为 **-1**，表示无限制。                             |

![](image/image_cE-umx1-1_.png)

### 3.3.5 将镜像推送到华为云镜像仓库

在**复制管理**页面，选择上一步骤中创建的规则，单击**复制**。可以手动同步存量的镜像到SWR实例。当相应的复制任务转化为**Succeeded**后，同步任务运行成功。

![](image/image_qrZiezzlie.png)

查看SWR上的镜像，显示多个busybox镜像。

![](image/image_7CVzHr0qGP.png)

***

# 4 总结

通过 Harbor 的镜像同步功能，可以轻松地将本地镜像推送到阿里云或华为云等不同场景。这种方式在多云环境中非常有用，既简化了镜像管理的流程，又提高了仓库管理的安全性和高效性。希望通过本文，帮助你在多云环境中高效管理镜像仓库。

***

# **参考文献**

- \[1][Harbor 官方文档](https://goharbor.io/docs/ "Harbor 官方文档")
- \[2][阿里云容器镜像服务文档](https://help.aliyun.com/zh/acr/use-cases/use-the-remote-replication-capability-of-harbor-to-synchronize-mirroring-to-an-acr-enterprise-edition-instance "阿里云容器镜像服务文档")
- \[3][华为云容器镜像服务文档](https://support.huaweicloud.com/bestpractice-swr/swr_bestpractice_0004.html "华为云容器镜像服务文档")
