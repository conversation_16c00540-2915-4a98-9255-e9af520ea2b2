# 一文掌握Harbor版本升级以及镜像迁移

## 目录

- [Harbor 概述](#Harbor-概述)
  - [Harbor 版本发布规律](#Harbor-版本发布规律)
- [Harbor 升级步骤](#Harbor-升级步骤)
  - [1. 停止当前 Harbor 实例](#1-停止当前-Harbor-实例)
  - [2. 备份现有 Harbor 数据](#2-备份现有-Harbor-数据)
  - [3. 下载并解压新版本 Harbor](#3-下载并解压新版本-Harbor)
  - [4. 更新配置文件](#4-更新配置文件)
  - [5. 启动新版本 Harbor](#5-启动新版本-Harbor)
  - [6. 验证升级结果](#6-验证升级结果)
- [Harbor 镜像复制](#Harbor-镜像复制)
  - [Replication 功能特性详解](#Replication-功能特性详解)
  - [配置目标 Harbor 实例](#配置目标-Harbor-实例)
  - [创建复制规则](#创建复制规则)
  - [执行镜像复制](#执行镜像复制)
- [总结](#总结)
  - [关键要点回顾](#关键要点回顾)
  - [实战经验分享](#实战经验分享)
  - [故障排除小贴士](#故障排除小贴士)
- [参考资料](#参考资料)

![  ](image/harbor-logo1_Z-Eew7Erkm.png "  ")

---

📚  **博客主页：** \<font face="黑体" size=5.5>[**StevenZeng学堂**](https://blog.csdn.net/u013522701?spm=1010.2135.3001.5343 "StevenZeng学堂")\</font>

🎉  **博客专栏:** &#x20;

- [**一文读懂Kubernetes**](https://blog.csdn.net/u013522701/category_12778372.html?spm=1001.2014.3001.5482 "一文读懂Kubernetes")&#x20;
- [**一文读懂Harbor**](https://blog.csdn.net/u013522701/category_12778948.html?spm=1001.2014.3001.5482 "一文读懂Harbor")
- [**云原生安全实战指南**](https://blog.csdn.net/u013522701/category_12785632.html "云原生安全实战指南")&#x20;
- [**云原生存储实践指南**](https://blog.csdn.net/u013522701/category_12811050.html?spm=1001.2014.3001.5482 "云原生存储实践指南")

---

![云原生技术分享动图](https://img-blog.csdnimg.cn/img_convert/c9fc944bbbaf0e567b4659df99169fed.gif)

---

> ❤️ 摘要： 在现代云原生架构中，容器镜像仓库是不可或缺的基础设施之一。[Harbor](https://goharbor.io/ "Harbor") 是一个开源的企业级容器镜像管理平台，支持镜像签名、漏洞扫描、权限控制等功能，广泛应用于企业容器化环境。本文将详细介绍 Harbor 的版本升级流程以及镜像迁移操作，并总结关键注意事项。

---

**💯 本文关联好文:**

- [《一文读懂Harbor以及部署实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142024722 "《一文读懂Harbor以及部署实践攻略》")
- [《【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践》](https://stevenzeng.blog.csdn.net/article/details/142470707 "【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践")
- [《一文掌握Harbor镜像同步公有云镜像仓库实践》](https://stevenzeng.blog.csdn.net/article/details/142649705 "一文掌握Harbor镜像同步公有云镜像仓库实践")

---

✍🏻 **本文知识点：**

- 版本升级
- 镜像复制

---

## Harbor 概述

Harbor 是由 VMware 开源的企业级容器镜像仓库解决方案，凭借其开源、安全、易用等特性，已成为现代企业私有化部署的首选方案。就像给容器镜像建了一个"五星级酒店"，不仅提供安全的住宿环境，还有门禁管理、安全检查、礼宾服务等贴心功能。

随着 Harbor 的持续迭代，新版本不断带来性能优化、安全增强以及令人兴奋的新特性。为了充分享受这些"免费升级服务"，定期升级 Harbor 已成为运维团队的重要任务之一。

### Harbor 版本发布规律

![Harbor 版本发布历史](image/Snipaste_2025-07-08_08-41-53_qy8wpjNRDV.png)

Harbor 的版本管理遵循语义化版本控制（Semantic Versioning）规范：

- **版本号格式**：`主版本号.次版本号.修订号`，例如 `v2.13.0`
  - 主版本号：重大架构变更或不兼容的 API 修改
  - 次版本号：新功能添加，向后兼容
  - 修订号：问题修复和小幅改进
- **候选版本**：版本号后缀 `rc`（Release Candidate），如 `v2.13.0-rc1`，表示发布前的候选版本，通常经过充分测试后转为正式版本
- **发布频率**：从图中可以看出，Harbor 发版相当频繁，基本保持每月一个新版本的节奏，这体现了社区的活跃度和对用户需求的快速响应

---

## Harbor 升级步骤

> 💡 **升级小贴士**：Harbor 升级虽非强制，但长期不升级就像开着老爷车上高速——虽然能跑，但安全风险不容忽视。升级 Harbor 需要像做外科手术一样谨慎，确保数据安全和业务连续性是首要目标。

### 1. 停止当前 Harbor 实例

升级前的第一步，就像给病人做手术前先打麻醉——温柔地让 Harbor 进入"休眠"状态。

```bash
cd harbor
docker compose down
```

**操作说明**：

- 该命令会优雅地停止所有 Harbor 相关容器
- 确保所有正在进行的操作完成后再停止服务
- 避免数据损坏或丢失

### 2. 备份现有 Harbor 数据

俗话说"不怕一万，就怕万一"，备份就是我们的"后悔药"。

```bash
# 创建备份目录
mkdir -p /opt/backup/$(date +%Y%m%d)

# 备份 Harbor 安装目录
cp -ar harbor /opt/backup/$(date +%Y%m%d)/harbor

# 备份数据库（默认路径为 /data/database）
cp -ar /data/database /opt/backup/$(date +%Y%m%d)/database

# 打包备份（推荐，节省空间）
tar -czf /opt/backup/harbor-backup-$(date +%Y%m%d-%H%M%S).tar.gz \
    harbor /data/database
```

**备份检查清单**：

- ✅ Harbor 配置文件（harbor.yml）
- ✅ 数据库文件（PostgreSQL 数据）
- ✅ 镜像存储数据（如使用本地存储）
- ✅ SSL 证书文件

### 3. 下载并解压新版本 Harbor

选择升级版本就像选择旅行路线——不是所有路都能直达目的地，有些需要中转。

**版本兼容性检查**：

在选择升级版本前，务必查阅 [Harbor 官方升级文档](https://goharbor.io/docs/2.12.0/administration/upgrade/)，确认升级路径的兼容性。

![Harbor 版本升级路径图](image/image_jm6LW7obze.png)

![Harbor 版本兼容性矩阵](image/image_f69nr3LVJj.png)

**升级路径示例**：

- 当前版本：v2.7.4
- 目标版本：v2.12.4
- 升级路径：v2.7.4 → v2.10.x → v2.12.4（需要分步升级）

> 💡 **实战策略**：考虑到生产环境的业务连续性要求，本文采用"新建+迁移"的方式，而非原地升级。这样既避免了长时间停机，又降低了升级风险。

**下载新版本**：

从 [Harbor 官方发布页面](https://github.com/goharbor/harbor/releases) 下载目标版本的离线安装包。

![Harbor 发布页面](image/image_OzoHglhYS1.png)

```bash
# 下载 Harbor v2.12.4 离线安装包
wget https://github.com/goharbor/harbor/releases/download/v2.12.4/harbor-offline-installer-v2.12.4.tgz

# 解压安装包
tar -xzvf harbor-offline-installer-v2.12.4.tgz

# 查看解压后的目录结构
ls -la harbor/
```

> ⚠️ **版本选择建议**：生产环境建议避免使用 `.0` 结尾的新版本（如 v2.13.0），优先选择经过社区验证的稳定版本（如 v2.12.4），这样可以避免踩到"新鲜出炉"的 bug。

### 4. 更新配置文件

配置文件的迁移就像搬家时整理物品——既要保留重要的东西，又要适应新环境。

```bash
# 复制旧版本配置文件作为参考
cp /opt/backup/$(date +%Y%m%d)/harbor/harbor.yml harbor/harbor.yml.backup

# 基于新版本模板创建配置文件
cp harbor.yml.tmpl harbor.yml

# 根据备份的配置文件，手动调整新配置
# 主要关注以下配置项：
# - hostname（主机名）
# - https 证书配置
# - harbor_admin_password（管理员密码）
# - database 配置
# - storage_service 配置
```

**配置迁移检查清单**：

- ✅ 主机名和端口配置
- ✅ HTTPS 证书路径
- ✅ 数据库连接配置
- ✅ 存储后端配置
- ✅ 外部服务集成（如 LDAP、OIDC）

运行配置准备脚本，生成最终的配置文件：

```bash
./prepare --with-trivy
```

### 5. 启动新版本 Harbor

万事俱备，只欠东风！现在可以启动全新的 Harbor 实例了。

**首次启动**（推荐使用安装脚本）：

```bash
# 使用安装脚本启动，包含 Trivy 漏洞扫描功能
./install.sh --with-trivy

# 或者根据需要选择其他组件
# ./install.sh --with-notary --with-trivy --with-chartmuseum
```

**后续启动**（使用 Docker Compose）：

```bash
docker compose up -d
```

### 6. 验证升级结果

升级完成后，需要进行全面的健康检查，确保所有组件都正常运行。

**检查容器状态**：

```bash
# 查看所有 Harbor 容器状态
docker compose ps

# 示例输出（所有容器都应该显示 healthy 状态）
NAME                IMAGE                                 COMMAND                  SERVICE       STATUS
harbor-core         goharbor/harbor-core:v2.12.4          "/harbor/entrypoint.…"   core          Up 18 hours (healthy)
harbor-db           goharbor/harbor-db:v2.12.4            "/docker-entrypoint.…"   postgresql    Up 18 hours (healthy)
harbor-jobservice   goharbor/harbor-jobservice:v2.12.4    "/harbor/entrypoint.…"   jobservice    Up 18 hours (healthy)
harbor-log          goharbor/harbor-log:v2.12.4           "/bin/sh -c /usr/loc…"   log           Up 18 hours (healthy)
harbor-portal       goharbor/harbor-portal:v2.12.4        "nginx -g 'daemon of…"   portal        Up 18 hours (healthy)
nginx               goharbor/nginx-photon:v2.12.4         "nginx -g 'daemon of…"   proxy         Up 18 hours (healthy)
redis               goharbor/redis-photon:v2.12.4         "redis-server /etc/r…"   redis         Up 18 hours (healthy)
registry            goharbor/registry-photon:v2.12.4      "/home/<USER>/entryp…"   registry      Up 18 hours (healthy)
registryctl         goharbor/harbor-registryctl:v2.12.4   "/home/<USER>/start.…"   registryctl   Up 18 hours (healthy)
```

**验证 Web 界面**：

通过浏览器访问 Harbor Web 界面，确认升级成功且服务正常运行。

![Harbor 升级后的 Web 界面](image/image_50Vt7Wnf8m.png)

**功能验证清单**：

- ✅ 登录功能正常
- ✅ 项目列表显示正确
- ✅ 镜像推送/拉取功能
- ✅ 用户权限管理
- ✅ 漏洞扫描功能（如启用了 Trivy）

---

## Harbor 镜像复制

镜像复制就像是给容器镜像安排"搬家服务"——既要保证东西不丢，又要确保搬到新家后一切正常。在混合云或多中心环境中，这项功能尤为重要。

Harbor 的镜像复制功能不仅可以用于多地域部署，在版本升级和数据迁移场景中也是不可或缺的利器。

### Replication 功能特性详解

Harbor 的复制功能就像一个智能的"快递员"，具备以下特性：

**🔄 资源同步能力**：

- 支持镜像和 Helm Charts 的跨仓库复制
- 包含镜像本身及相关的 Cosign 签名（镜像安全验证的重要组成部分）
- 保持数据完整性和一致性

**⚡ 灵活的触发机制**：

- **手动触发**：按需执行复制任务
- **定时触发**：基于 Cron 表达式的自动化复制
- **事件触发**：镜像推送时自动触发复制

**🏗️ 智能命名空间管理**：

- 目标仓库中不存在的命名空间会自动创建
- 权限检查：确保用户账户具有目标命名空间的写权限

**🔒 安全性考虑**：

- 复制仅涉及镜像资源本身
- 不会复制项目成员权限和敏感配置信息

**🔄 可靠性保障**：

- 内置重试机制：失败任务会自动重新调度
- 网络容错：应对网络延迟和临时故障

**⚠️ 版本兼容性限制**：

- 源仓库和目标仓库的 Harbor 版本需要兼容
- Harbor v1.x 和 v2.x 之间由于 API 差异较大，通常不支持直接复制

### 配置目标 Harbor 实例

在新的 Harbor 实例中配置旧 Harbor 作为复制源，就像给两个仓库之间搭建一座"数据桥梁"。

**操作步骤**：

1. 登录新的 Harbor 实例
2. 导航至：**系统管理** → **仓库管理** → **新建目标**

![Harbor 仓库管理界面](image/image_cUMQ7bXZDG.png)

**配置参数详解**：

| 配置项                 | 说明                 | 示例值                             |
| ---------------------- | -------------------- | ---------------------------------- |
| **提供商**       | 选择源仓库类型       | Harbor（或 Docker Registry）       |
| **目标名**       | 为这个连接起个名字   | `old-harbor-source`              |
| **目标URL**      | 源 Harbor 的访问地址 | `https://old-harbor.company.com` |
| **访问ID**       | 源仓库的用户名       | `admin`（建议使用管理员账户）    |
| **访问密码**     | 对应的密码           | `Harbor12345`                    |
| **验证远程证书** | 自签名证书请取消勾选 | ❌（生产环境建议使用正式证书）     |

**支持的提供商类型**：

| 提供商                                 | 描述                    | 适用场景            |
| -------------------------------------- | ----------------------- | ------------------- |
| **Docker Hub**                   | Docker 官方公共镜像仓库 | 同步公共镜像        |
| **Docker Registry**              | 标准 Docker Registry v2 | 私有部署的 Registry |
| **Harbor**                       | Harbor 企业级仓库       | Harbor 间的镜像迁移 |
| **AWS ECR**                      | 亚马逊云容器镜像服务    | AWS 生态集成        |
| **Azure ACR**                    | 微软 Azure 容器镜像服务 | Azure 生态集成      |
| **阿里云 ACR**                   | 阿里云容器镜像服务      | 阿里云生态集成      |
| **Google GCR/Artifact Registry** | Google 云镜像服务       | Google Cloud 生态   |
| **华为云 SWR**                   | 华为云容器镜像服务      | 华为云生态集成      |
| **GitLab Registry**              | GitLab 内置镜像仓库     | CI/CD 流水线集成    |
| **Quay**                         | Red Hat 镜像托管服务    | 企业级镜像管理      |
| **JFrog Artifactory**            | 通用制品管理平台        | 多格式制品管理      |

![Harbor 目标配置界面](image/image_y93c1e5SS8.png)

**连接测试**：

配置完成后，点击"测试连接"按钮。如果显示连接成功，说明配置正确，可以进行下一步操作。

![连接测试成功](image/image_gZijP0qGm0.png)

> 💡 **配置小贴士**：
>
> - 建议使用管理员账户进行复制，确保有足够的权限访问所有项目
> - 如果使用自签名证书，务必取消"验证远程证书"选项
> - 测试连接成功是后续复制操作的前提条件

## 创建复制规则

在新 Harbor 实例中，进入“系统管理”-“复制管理”页面，添加新的复制规则。

![](image/image_i293fvFEOR.png)

---

### **配置项说明**

> 当前方案是通过主动pull的方式同步镜像

1. **复制模式**
   - **Push-based**（基于推送）：将镜像从本地仓库复制到远程仓库。
   - **Pull-based**（基于拉取）：从远程仓库拉取镜像到本地仓库。
2. **源仓库 (Source)**：定义从哪个镜像仓库拉取资源。这里选择上面定义的目标仓库；
3. **源资源过滤器 (Filters)**
   - **名称过滤器 (Name Filter)**：根据镜像或资源的名称进行匹配，支持通配符匹配任意非分隔符字符。
   - **标签过滤器 (Tag Filter)**：根据镜像的标签（Tag）进行匹配，同样支持通配符。
   - **标签过滤器 (Label Filter)**：根据资源的标签（Label）进行匹配，可以选择匹配或排除特定标签。
   - **资源类型 (Resource Type)**：
     - 可选范围包括：
       - **镜像 (Images)**：仅同步镜像。
       - **工件 (Artifacts)**：同步所有 OCI 兼容的资源（包括镜像和其他类型）。
       - **全部 (All)**：同步所有资源。
   - 通配符支持的模式：
     - `*`：匹配任何非分隔符字符序列（`/`）。
     - `**`：匹配任何字符序列，包括路径分隔符（`/`），但必须作为路径组件单独出现。
     - `?`：匹配任何单个非分隔符字符（`/`）。
     - `{alt1,…}`：如果逗号分隔的替代方案之一匹配，则匹配字符序列。
4. **匹配/排除逻辑**
   - 每个过滤器都支持 **匹配 (Include)** 或 **排除 (Exclude)** 的逻辑。
   - 例如：
     - 匹配：仅同步符合过滤条件的资源。
     - 排除：跳过符合过滤条件的资源。
5. **目标**
   - 输入目标命名空间的名称。如果不输入命名空间，则资源将放置在与源仓库相同的命名空间中。
   - 仓库扁平化规则: 保持默认即可。

     ![](image/image_6g415EOt6K.png)
6. **触发方式**
   - 手动触发（**Manual**）：手动触发复制。
   - 定时触发(**Scheduled**）：通过定义 cron 作业定期复制。
   - 基于事件触发（**Event Based**）：当项目中推送新资源或重新标记工件时，立即复制到远程仓库。如果选择**Delete remote resources when locally deleted**，则本地删除的工件也会从复制目标中删除。
7. **带宽**：如果业务的带宽不够，则建议限制或者闲时再进行迁移。

---

## 执行镜像复制

对目标仓库的library项目的指定镜像复制，使用通配符筛选tag，比如library的nginx:1.0\*

![](image/image_RL9VAMbdri.png)

创建复制策略后，点击复制

![](image/image_hKQgUgV2z2.png)

点击确认。

![](image/image_IF8WI_yGPI.png)

查看复制任务

![](image/image_IcHbui1fbk.png)

可以看到匹配的镜像有两个镜像

![](image/image_iPtseNj8H7.png)

最终完成复制

![](image/image_rEjcTw-BKf.png)

检查镜像仓库，在library项目，确实同步了nginx镜像。

![](image/image_aKXCZwGTSz.png)

## 总结

Harbor 的版本升级和镜像迁移就像给企业的"数字资产"搬家——既要确保东西不丢，又要让新家比旧家更好用。通过本文的实践指南，相信您已经掌握了这项重要技能。

### 关键要点回顾

**🛡️ 安全第一原则**：

- 备份是一切操作的前提，就像给重要文件买保险
- 多重备份策略：配置文件 + 数据库 + 镜像数据

**🔄 升级策略选择**：

- 原地升级：适合测试环境，停机时间短
- 新建迁移：适合生产环境，风险可控

**📋 版本兼容性管理**：

- 仔细查阅官方升级路径文档
- 避免跨大版本直接升级
- 优先选择稳定版本而非最新版本

**🚀 镜像复制最佳实践**：

- 合理设置过滤规则，避免不必要的数据传输
- 利用定时任务实现自动化同步
- 监控复制任务状态，及时处理异常

### 实战经验分享

1. **升级时机选择**：建议在业务低峰期进行，给足够的时间窗口
2. **测试环境验证**：生产升级前务必在测试环境完整演练
3. **回滚预案**：准备快速回滚方案，以防升级失败
4. **监控告警**：升级后密切关注系统运行状态

### 故障排除小贴士

- **容器启动失败**：检查配置文件语法和权限设置
- **镜像复制失败**：验证网络连通性和账户权限
- **Web 界面异常**：清理浏览器缓存，检查证书配置
- **性能问题**：关注数据库连接数和存储 I/O 性能

通过掌握这些技能，您就能像一个经验丰富的"Harbor 船长"，在云原生的海洋中稳健航行，为企业的容器化之旅保驾护航！

---

## 参考资料

- [Harbor 复制配置官方文档](https://goharbor.io/docs/2.12.0/administration/configuring-replication/)
- [Harbor 升级指南](https://goharbor.io/docs/2.12.0/administration/upgrade/)
- [Harbor 官方 GitHub 仓库](https://github.com/goharbor/harbor)

---

🚀 **Happy Cloud Native Journey!**

如果您在 Harbor 升级或迁移过程中遇到任何问题，欢迎在评论区留言交流！也欢迎关注我的博客，获取更多云原生技术实践分享。让我们一起在云原生的道路上越走越远！
