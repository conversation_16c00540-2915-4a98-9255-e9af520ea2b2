# 一文掌握Harbor的后端存储管理

## 目录

[概述](#概述)

- [Harbor 支持多种存储](#Harbor-支持多种存储)
- [Harbor 后端存储架构](#Harbor-后端存储架构)
  - [Harbor 存储架构原理](#Harbor-存储架构原理)
  - [支持的存储后端类型](#支持的存储后端类型)
  - [各种存储后端的比较](#各种存储后端的比较)

[实践案例](#实践案例)

- [Harbor + NFS 实践](#Harbor--NFS-实践)
  - [NFS配置步骤](#NFS配置步骤)
  - [配置Harbor使用NFS存储](#配置Harbor使用NFS存储)
- [Harbor + Minio 实践](#Harbor--Minio-实践)
  - [MinIO部署步骤](#MinIO部署步骤)
  - [MinIO配置步骤](#MinIO配置步骤)
  - [配置Harbor使用MinIO存储](#配置Harbor使用MinIO存储)
- [Harbor + 阿里云OSS 实践](#Harbor--阿里云OSS-实践)
  - [阿里云OSS创建Bucket](#阿里云OSS创建Bucket)
  - [阿里云创建AccessKey](#阿里云创建AccessKey)
  - [对RAM授权OSS](#对RAM授权OSS)
  - [使用ossutil验证阿里云OSS](#使用ossutil验证阿里云OSS)
  - [配置Harbor使用阿里云OSS存储](#配置Harbor使用阿里云OSS存储)
  - [安装并启动Harbor](#安装并启动Harbor)

[总结](#总结)

[参考](#参考)

---



![  ](image/harbor-logo1_b_wTDow9BY.png "  ")

📚  **博客主页：** \<font face="黑体" size=5.5>[**StevenZeng学堂**](https://blog.csdn.net/u013522701?spm=1010.2135.3001.5343 "StevenZeng学堂")\</font>

🎉  **博客专栏:** &#x20;

- [**一文读懂Kubernetes**](https://blog.csdn.net/u013522701/category_12778372.html?spm=1001.2014.3001.5482 "一文读懂Kubernetes")&#x20;
- [**一文读懂Harbor**](https://blog.csdn.net/u013522701/category_12778948.html?spm=1001.2014.3001.5482 "一文读懂Harbor")
- [**云原生安全实战指南**](https://blog.csdn.net/u013522701/category_12785632.html "云原生安全实战指南")&#x20;
- [**云原生存储实践指南**](https://blog.csdn.net/u013522701/category_12811050.html?spm=1001.2014.3001.5482 "云原生存储实践指南")

---

![](https://img-blog.csdnimg.cn/img_convert/c9fc944bbbaf0e567b4659df99169fed.gif)

---

> ❤️ 摘要： 本文全面介绍Harbor企业级容器镜像仓库的后端存储管理，详细讲解了Harbor支持的多种存储后端配置方法，包括文件系统、NFS、MinIO、阿里云OSS等。通过丰富的配置示例、最佳实践指南和生产环境部署案例，帮助读者掌握Harbor存储架构设计原理，学会根据不同场景选择合适的存储方案。本文适合容器化运维工程师、云原生架构师以及对企业级镜像仓库管理感兴趣的技术人员。

---

**💯 本文关联好文:**

- [《一文读懂Harbor以及部署实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142024722 "《一文读懂Harbor以及部署实践攻略》")
- 《[【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践](https://stevenzeng.blog.csdn.net/article/details/142470707 "【云原生安全篇】Trivy助力离线Harbor漏洞扫描实践")》
- 《[一文掌握Harbor镜像同步公有云镜像仓库实践](https://stevenzeng.blog.csdn.net/article/details/142649705 "一文掌握Harbor镜像同步公有云镜像仓库实践")》

---

✍🏻**本文知识点:**

- Harbor 存储架构原理与设计
- 多种存储后端配置方法（NFS、MinIO、阿里云OSS等）
- 生产环境部署最佳实践
- 存储后端性能对比与选型指南

# 概述

## Harbor 支持多种存储

**Harbor** 是一个高效的开源企业级容器镜像仓库，它支持多种类型的存储后端来保存容器镜像数据，包括Filesystem、S3、OSS等，在企业部署中，选择合适的存储后端至关重要，因为它会影响系统的可扩展性、可靠性和性能。

## Harbor 后端存储架构

Harbor 作为企业级容器镜像仓库，其存储架构设计灵活且可扩展。Harbor 基于 Docker Distribution（Registry v2）构建，支持多种存储后端来满足不同规模和场景的需求。

### Harbor 存储架构原理

Harbor 的存储架构采用分层设计：

- **Registry 层**：负责镜像的存储和分发，基于 Docker Distribution
- **存储驱动层**：提供统一的存储接口，支持多种后端存储
- **数据持久化层**：实际的存储介质，可以是本地文件系统或云存储服务

![Docker-Distribution Docker-Distribution ](image/image_zc-s4dFE1D.png "Docker-Distribution Docker-Distribution ")

### 支持的存储后端类型

Harbor 支持以下几种存储后端：

- **文件系统（FileSystem）**：默认的本地存储方案，数据存储在本地磁盘上，适合小规模的测试或开发环境。
- **NFS（网络文件系统）**：通过网络共享文件系统提供分布式存储，支持多节点共享访问。
- **Ceph**：分布式对象存储系统，提供高可用性和可扩展性，适合大规模集群环境。
- **S3**：符合Amazon S3接口协议的对象存储服务都支持，其代表有Amazon S3、MinIO开源存储，都具有高可用性特性。
- **阿里云 OSS**：阿里云的对象存储服务，类似 AWS S3，在中国地区具有更好的网络性能。
- **Google Cloud Storage (GCS)**：Google 云平台的对象存储服务。
- **Azure Blob Storage**：Microsoft Azure 的对象存储服务。

### 各种存储后端的比较

| 存储类型             | 优点                                             | 缺点                                     | 适用场景                                         |
| -------------------- | ------------------------------------------------ | ---------------------------------------- | ------------------------------------------------ |
| **FileSystem** | 简单易部署，快速应用                             | 性能和存储容量有明显限制                 | 小规模企业或开发测试环境                         |
| **NFS**        | 简单易部署，适合共享存储                         | 性能相对较低，在高并发场景下容易成为瓶颈 | 小规模企业或开发测试环境                         |
| **Ceph**       | 高可用、高扩展性，支持块存储、文件存储、对象存储 | 部署复杂，运维成本高                     | 大规模集群环境，尤其是需要高可用和海量存储的场景 |
| **AWS S3**     | 高度可扩展，全球服务，集成方便                   | 网络延迟较大（跨区域访问），费用可能较高 | 云原生应用、跨地域的企业应用                     |
| **阿里云OSS**  | 高可用、低延迟，适合中国市场，易于集成           | 主要适用于阿里云生态，跨云供应商不够灵活 | 使用阿里云服务的企业，尤其是面向中国市场的应用   |

---

# 实践案例

> Harbor部署参考[《一文读懂Harbor以及部署实践攻略》](https://stevenzeng.blog.csdn.net/article/details/142024722 "《一文读懂Harbor以及部署实践攻略》")

## Harbor + NFS 实践

> **NFS（Network File System）** 是通过网络共享文件系统的一种方案，适用于多个节点共享存储的场景。

### NFS配置步骤

1. **安装NFS服务器**（在NFS服务器上）：

   ```bash
   apt-get install nfs-kernel-server
   ```
2. **配置NFS共享目录**（例如 `/data/harbor`）：

   编辑 `/etc/exports` 文件，添加以下内容：

   ```bash
   /data/harbor 192.168.0.0/24(rw,sync,no_subtree_check)
   ```

   这里 `192.168.0.0/24` 是允许访问的子网范围。
3. **启动NFS服务**：

   ```bash
   systemctl start nfs-kernel-server

   ```
4. 查看nfs状态

   ```bash
   exportfs -a
   ```

   输出

   ```bash
   exportfs: /etc/exports [2]: Neither 'subtree_check' or 'no_subtree_check' specified for export "192.168.0.0/24:/data/harbor".
     Assuming default behaviour ('no_subtree_check').
     NOTE: this default has changed since nfs-utils version 1.0.x

   ```

### **配置Harbor使用NFS存储**

> 如果Harbor第一次部署使用Filesysten作为后端服务，那么建议删除掉现有的镜像和项目，再进行后端存储切换，避免数据缺少导致的系统报错。

在配置前，确认Harbor所在主机是否能正常访问nfs存储。

```bash
showmount -e 172.16.100.81

```

输出

```markdown
Export list for 172.16.10.81:
/data/harbor 192.168.0.0/24
```

创建挂载点并手动挂载NFS目录到主机的 `/mnt/harbor` 路径：

```bash
mkdir /mnt/harbor
chown -R 10000:10000 /mnt/harbor
mount -t nfs 172.16.10.81:/data/harbor /mnt/harbor
```

或者使用fstab持久化挂载，在/etc/fstab添加以下配置：

```bash
172.16.10.81:/data/harbor  /mnt/harbor  nfs  defaults,nofail,_netdev,timeo=14,intr,rsize=1048576,wsize=1048576,vers=4  0  0

```

执行挂载

```bash
mount -a

```

查看挂载状态

```bash
root@rke2:/data# df |grep harbor
172.16.10.81:/data/harbor  514937856 49307648 439399424  11% /mnt/harbor

```

在 `harbor.yml` 文件中，配置 `storage_service` 使用 ` filesystem`：

```yaml
storage_service:
  filesystem:
    rootdirectory: /mnt/harbor
```

进入harbor目录，执行命令，重启harbor

```bash
./install.sh --with-trivy
```

上传一个image，这里使用复制功能同步library仓库镜像。

![](image/image_T0EZhzuQpU.png)

同步后发现任务执行失败了，查看日志

![](image/image_7STRvSWQvl.png)

> ❔ 在/mnt/harbor创建目录失败了？

进行分析原因，查看配置 `./common/config/registry/config.yml`

```yaml
version: 0.1
log:
  level: info
  fields:
    service: registry
storage:
  cache:
    layerinfo: redis
  filesystem:
    rootdirectory: /mnt/harbor
    maxthreads: 100

```

配置是正确的，那么可能是docker内映射的目录不正确。

修改docker-compose.yml

```yaml
  registry:
    image: goharbor/registry-photon:v2.12.4
    container_name: registry
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      #- /data/registry:/storage:z
      - /mnt/harbor:/mnt/harbor:z  # 新增目录映射
  registryctl:
    image: goharbor/harbor-registryctl:v2.12.4
    container_name: registryctl
    env_file:
      - ./common/config/registryctl/env
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      #- /data/registry:/storage:z
      - /mnt/harbor:/mnt/harbor:z # 新增目录映射


```

重新同步nginx镜像，可以看到有数据了。

![](image/image_NyLKi2oYcm.png)

![](image/image_NyLKi2oYcm.png)

查看nfs存储

```yaml
ls -l /mnt/harbor/docker/registry/v2/
```

看到已经有数据，证明配置成功。

![](image/image_IfpG64S7Lo.png)

❔ 思考： 能不能直接将NFS挂载到Harbor的registry目录呢？ 这样可以不改harbor.yml和docker-compose.yml。

---

## Harbor + Minio 实践

> MinIO 是一个**高性能、开源的对象存储系统**，兼容 Amazon S3 API。它专为云原生和容器化环境设计，适合存储非结构化数据，如图片、视频、日志文件、备份和容器/虚拟机镜像等。所以MinIO 替代传统的文件系统或NFS存储，可提供更好的性能和可扩展性。

因为MinIO部署不是重点，下面案例以MinIO单节点的方式快速拉起。

版本要求：

> docker-compose： v2.23.3
> docker：                 27.5.1

### MinIO部署步骤

创建MinIO目录

```bash
mkdir /data/minio/{config,volumes}

```

创建配置 `config/minio.conf`

```bash
# MINIO_ROOT_USER and MINIO_ROOT_PASSWORD sets the root account for the MinIO server.
# This user has unrestricted permissions to perform S3 and administrative API operations on any resource in the deployment.
# Omit to use the default values 'minioadmin:minioadmin'.
# MinIO recommends setting non-default values as a best practice, regardless of environment

MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=minio-test12345

# MINIO_VOLUMES sets the storage volume or path to use for the MinIO server.

MINIO_VOLUMES="/mnt/data"

# MINIO_OPTS sets any additional commandline options to pass to the MinIO server.
# For example, `--console-address :9001` sets the MinIO Console listen port
MINIO_OPTS="--console-address :9001"

```

创建 `docker-compose.yml`

```yaml
services:
  minio:
    tty: true
    ports:
      - 9000:9000
      - 9001:9001
    volumes:
      - ./volumes:/mnt/data
      - ./config/minio.conf:/etc/config.env
    environment:
      - MINIO_CONFIG_ENV_FILE=/etc/config.env
    container_name: minio_local
    image: quay.io/minio/minio:RELEASE.2023-03-20T20-16-18Z
    command: server --console-address ":9001"
networks: {}

```

拉起MinIO

```bash
docker-compose up -d
```

查看MinIO状态

```bash
NAME          IMAGE                                              COMMAND                  SERVICE   CREATED       STATUS       PORTS
minio_local   quay.io/minio/minio:RELEASE.2023-03-20T20-16-18Z   "/usr/bin/docker-ent…"   minio     5 weeks ago   Up 5 weeks   0.0.0.0:9000->9000/tcp, :::9000->9000/tcp, 0.0.0.0:9001->9001/tcp, :::9001->9001/tcp

```

查看MinIO日志

```bash
docker-compose logs -f
```

看输出，证明启动正常。

```markdown
minio_local  | MinIO Object Storage Server
minio_local  | Copyright: 2015-2023 MinIO, Inc.
minio_local  | License: GNU AGPLv3 <https://www.gnu.org/licenses/agpl-3.0.html>
minio_local  | Version: RELEASE.2023-03-20T20-16-18Z (go1.19.7 linux/amd64)
minio_local  |
minio_local  | Status:         1 Online, 0 Offline.
minio_local  | API: http://**********:9000  http://127.0.0.1:9000
minio_local  | RootUser: admin
minio_local  | RootPass: minio-test-lesso
minio_local  | Region:   cn-foshan
minio_local  | Console: http://**********:9001 http://127.0.0.1:9001
minio_local  | RootUser: admin
minio_local  | RootPass: minio-test12345
minio_local  |
minio_local  | Command-line: https://min.io/docs/minio/linux/reference/minio-mc.html#quickstart
minio_local  |    $ mc alias set myminio http://**********:9000 admin minio-test-lesso
minio_local  |
minio_local  | Documentation: https://min.io/docs/minio/linux/index.html
minio_local  | Warning: The standard parity is set to 0. This can lead to data loss.
minio_local  |
minio_local  |  You are running an older version of MinIO released 2 years ago
minio_local  |  Update: Run `mc admin update`

```

### MinIO配置步骤

点击identity-users，创建用户，命名为harbor，授权为consoleAdmin

![](image/image_oPTng5_QkL.png)

点击Bucket，创建harbor专属的bucket，命名为harbor1

![](image/image_zgS1JOi73b.png)

点击bucket-access-user，查看桶授权，确认是否授权

![](image/image_KK4FIFGMMj.png)

点击settings-region，设置区域，命名为cn-foshan

![](image/image_L54p6J1F7H.png)

### **配置Harbor使用MinIO存储**

> 修改后端存储前，把现有的项目和镜像清除。

在 `harbor.yml` 文件中，配置 `storage_service` 使用 `s3`：

```yaml
storage_service:
    s3:
      accesskey: harbor
      secretkey: Harbor@2025
      region: cn-foshan
      regionendpoint: http://172.16.10.82:9000
      bucket: harbor1
      loglevel: debug

```

重新生成配置，并重启harbor

```bash
./install.sh --with-trivy
```

重新同步镜像，可以看到有数据了。

![](image/image_ERUyWR7Nlv.png)

登录MinIO的Web UI，查看Bucket的数据，也能看到有数据了，证明配置成功。

![](image/image_oTeYwizRkL.png)

## Harbor + 阿里云OSS 实践

> 阿里云对象存储服务（Object Storage Service，简称OSS）是阿里云提供的**海量、安全、低成本、高可靠的云存储服务**，同样兼容S3协议，Harbor通过S3兼容接口使用OSS作为存储后端。

> 💖 Harbor没有上云， 因此主要区别是阿里云的OSS创建和配置的过程。

### 阿里云OSS创建Bucket

打开[阿里云OSS的管理界面](https://oss.console.aliyun.com/overview "阿里云OSS的管理界面")

![](image/image_JkRT1qBNOL.png)

创建Harbor的Bucket

![](image/image_GXRCQN2n5S.png)

填写相关的参数，选择离企业近的区域，减少网络延迟。

![](image/image_1DebvVnTlM.png)

确认完成创建

![](image/image_83WbX7VGYI.png)

![](image/image_lqWq1osMm3.png)

### 阿里云创建 `AccessKey`

点击右上角的用户头像，点击安全策略-身份管理-用户，创建新用户 `harbor`

![](image/image_f0TW0QGwdY.png)

![](image/image_WFrp2M3Op4.png)

完成用户创建后，创建 `AccessKey`

![](image/image_Iyvsg10UTj.png)

![](image/image_2QILAxzBZt.png.mark.png)

记住创建后保存 `AccessKey`和 `AccessSecret`

![](image/image_JlGGVQQTj_.png.mark.png)

### 对RAM授权OSS

对用户进行授权， 点击权限管理-授权，选择用户和权限（AliyunOSSFullAccess）

![](image/image_la52wvN67g.png)

### 使用ossutil验证阿里云OSS

安装ossutil

```bash
sudo -v ; curl https://gosspublic.alicdn.com/ossutil/install.sh | sudo bash
```

配置oss config

```bash
ossutil config
```

输出

```markdown

The command creates a configuration file and stores credentials.

Please enter the config file name,the file name can include path(default /root/.ossutilconfig, carriage return will use the default file. If you specified this option to other file, you should specify --config-file option to the file when you use other commands):
No config file entered, will use the default config file /root/.ossutilconfig

For the following settings, carriage return means skip the configuration. Please try "help config" to see the meaning of the settings
Please enter language(CH/EN, default is:EN, the configuration will go into effect after the command successfully executed): <CH>
Please enter endpoint:         <Bucket所在区域的外网Endpoint>
Please enter accessKeyID:      <your-aliyun-access-key-id>
Please enter accessKeySecret:  <your-aliyun-access-key-secret>
Please enter stsToken:         <留空即可>

```

验证，登录成功

```bash
root@rke2# ossutil ls oss://harbor2.oss-cn-guangzhou.aliyuncs.com
Error: bucket name harbor2.oss-cn-guangzhou.aliyuncs.com can only include lowercase letters, numbers, and -
root@rke2-rancher:/opt/stacks/harbor# ossutil ls oss://harbor2
Object Number is: 0

0.158653(s) elapsed

```

### 配置Harbor使用阿里云OSS存储

编辑 `harbor.yml` 文件，配置存储服务为 OSS：

```yaml
storage_service:
  ca_bundle:
  oss:
    accesskeyid: <your-aliyun-access-key-id>
    accesskeysecret: <your-aliyun-access-key-secret>
    region: <your-aliyun-oss-region>  # 使用OSS专用地域ID
    bucket: <your-aliyun-oss-bucket>  
    secure: true                      # 是否使用https
    internal: false                   # harbor是否部署在aliyun，如果上云，设置为true
    encrypt: false                    # 传输是否加密
    chunksize: 10485760               # chunk的单位bit
    rootdirectory: /storage           # oss挂载路径

```

> ⭐[ 阿里云region列表](https://help.aliyun.com/zh/oss/user-guide/regions-and-endpoints?spm=a2c4g.11186623.help-menu-31815.d_4_0_0.180b7368LgXu68 " 阿里云region列表")

### 安装并启动Harbor

执行以下命令安装并启动 Harbor：

```bash
./install.sh --with-trivy
```

测试推送镜像到Harbor

![](image/image_-JVZS-qDP0.png)

查看任务日志，显示正常推送

![](image/image_OIrVFn01fK.png)

查看仓库和镜像

![](image/image_3d7Q_xAClH.png)

通过ossutil查看OSS

```bash
ossutil ls oss://harbor2

```

输出

```markdown
LastModifiedTime                   Size(B)  StorageClass   ETAG                                  ObjectName
2025-07-10 09:12:47 +0800 CST        26729      Standard   3AA261DEA6FFBE16FB4412556D1380B4-1    oss://harbor2/storage/docker/registry/v2/blobs/sha256/35/3507c1bbf532bc8917d870562b19249714ff5567ea5d8af3057b46a20a2f36c4/data
2025-07-10 09:12:51 +0800 CST         1367      Standard   5F4F32496C8E63DEF77D1358E053E867      oss://harbor2/storage/docker/registry/v2/blobs/sha256/76/76e9d6514bbbe64b8a679218568a50ec5aef599c587980f7e30e4f9efd80ebe6/data
2025-07-10 09:12:50 +0800 CST       133745      Standard   625498CBE84AA8AB70FF571015D9A022-1    oss://harbor2/storage/docker/registry/v2/blobs/sha256/8c/8c1054bf1c452f86b6212bdb190f5a9e3f3e7d43928ee310499ad39526556142/data
2025-07-10 09:12:31 +0800 CST         2742      Standard   7A1789076288C216FDB9B0088AD48D66-1    oss://harbor2/storage/docker/registry/v2/blobs/sha256/d0/d0cee038721ff10a118ab1d62bceff9163f0f380f87d2c6dcab385379a6af4ad/data
2025-07-10 09:12:48 +0800 CST        23590      Standard   B6AA6BE920E08F5DEA040761C5498CB5-1    oss://harbor2/storage/docker/registry/v2/blobs/sha256/ea/eac171c43ed007b2b6dea10ebf180132702c47a8737f309b0da0fee845aaf0e9/data
2025-07-10 09:12:45 +0800 CST      4807021      Standard   C75C91BFD0ACF60637E4F12E39E43F1B-1    oss://harbor2/storage/docker/registry/v2/blobs/sha256/f6/f6d084ccf1d56475a7a3d8833c9826b5f125441d34f909744f8911b8d05d125c/data
2025-07-10 09:12:36 +0800 CST      2065537      Standard   5CE33FCFB0E3E12B82BFF594DB120452-1    oss://harbor2/storage/docker/registry/v2/blobs/sha256/ff/ff3a5c916c92643ff77519ffa742d3ec61b7f591b6b7504599d95a4a41134e28/data
2025-07-10 09:12:47 +0800 CST           71      Standard   EE977BCE3F749E6EB7463EA0A497A99D      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_layers/sha256/3507c1bbf532bc8917d870562b19249714ff5567ea5d8af3057b46a20a2f36c4/link
2025-07-10 09:12:50 +0800 CST           71      Standard   32313827CA97152042E22A03BA89FC07      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_layers/sha256/8c1054bf1c452f86b6212bdb190f5a9e3f3e7d43928ee310499ad39526556142/link
2025-07-10 09:12:31 +0800 CST           71      Standard   B5A6D2C329C01898189AE426DAF80172      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_layers/sha256/d0cee038721ff10a118ab1d62bceff9163f0f380f87d2c6dcab385379a6af4ad/link
2025-07-10 09:12:49 +0800 CST           71      Standard   9EE8DFD590E8F24148C54B3696D7FE5D      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_layers/sha256/eac171c43ed007b2b6dea10ebf180132702c47a8737f309b0da0fee845aaf0e9/link
2025-07-10 09:12:46 +0800 CST           71      Standard   2A9893D7D32D038A6F92BB41C94B147E      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_layers/sha256/f6d084ccf1d56475a7a3d8833c9826b5f125441d34f909744f8911b8d05d125c/link
2025-07-10 09:12:37 +0800 CST           71      Standard   3F529A5EDBB6B5CE61308C6B200925F5      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_layers/sha256/ff3a5c916c92643ff77519ffa742d3ec61b7f591b6b7504599d95a4a41134e28/link
2025-07-10 09:21:47 +0800 CST           71      Standard   7554896F4CA6D2238D0D41DD055A3340      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_manifests/revisions/sha256/76e9d6514bbbe64b8a679218568a50ec5aef599c587980f7e30e4f9efd80ebe6/link
2025-07-10 09:21:47 +0800 CST           71      Standard   7554896F4CA6D2238D0D41DD055A3340      oss://harbor2/storage/docker/registry/v2/repositories/library/dnstools/_manifests/tags/latest/current/link
```

在OSS管理界面查看Bucket内容

![](image/image_0iOCaLUyiz.png)

# 总结

通过本文的详细介绍和实践案例，我们全面掌握了Harbor的后端存储管理。从理论到实践，从配置到部署，本文为读者提供了完整的Harbor存储后端解决方案。

通过本文的学习，读者应该能够：

- 理解Harbor存储架构的设计原理
- 掌握各种存储后端的配置方法
- 具备生产环境部署和运维的能力
- 能够根据实际需求选择合适的存储方案

Harbor作为企业级容器镜像仓库，其灵活的存储后端支持为不同规模和需求的企业提供了完整的解决方案。正确选择和配置存储后端，是构建稳定、高效的容器镜像管理平台的关键基础。

---

# 参考

- \[1] [Configuring a registry](https://distribution.github.io/distribution/about/configuration/ "Configuring a registry")
- \[2] [Configure the Harbor YML File](https://goharbor.io/docs/2.12.0/install-config/configure-yml-file/ "Configure the Harbor YML File")
- \[3] [Aliyun OSS操作指南](https://help.aliyun.com/zh/oss/user-guide/?spm=a2c4g.11186623.help-menu-31815.d_4.31165a057hCrx3 "Aliyun OSS操作指南")
- \[4] [Aliyun 访问控制操作指南](https://help.aliyun.com/zh/ram/user-guide/?spm=a2c4g.11174283.help-menu-28625.d_2.609a1f49axurT4 "Aliyun 访问控制操作指南")
