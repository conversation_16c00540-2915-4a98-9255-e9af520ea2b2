# 【云原生安全篇】一文读懂Trivy&#x20;

## 目录

- [1 概念](#1-概念)
  - [1.1 什么是 Trivy？](#11-什么是-Trivy)
  - [1.2 Trivy 的工作原理](#12-Trivy-的工作原理)
    - [1.2.1 Trivy的组件](#121-Trivy的组件)
    - [1.2.2 组件介绍](#122-组件介绍)
    - [Trivy 的工作原理](#Trivy-的工作原理)
- [2 小试牛刀：Trivy 实践案例](#2-小试牛刀Trivy-实践案例)
  - [安装 Trivy](#安装-Trivy)
    - [2.1.1 通过包管理工具安装](#211-通过包管理工具安装)
    - [2.1.2 通过二进制方式安装](#212-通过二进制方式安装)
    - [2.1.3 查看trivy版本](#213-查看trivy版本)
  - [2.2 扫描容器镜像](#22-扫描容器镜像)
  - [2.3 扫描项目文件](#23-扫描项目文件)
  - [2.4 扫描Kubernetes](#24-扫描Kubernetes)
- [3 总结](#3-总结)
- [4 参考资料](#4-参考资料)

> ❤️ 摘要：在云原生和容器化应用飞速发展的时代，安全问题变得愈加突出。如何确保我们的容器镜像、Kubernetes 集群、基础设施即代码 (IaC) 等组件的安全，是每个开发者和运维工程师关注的焦点。而 **Trivy** 作为一款开源的全能安全扫描工具，帮助我们快速发现和定位安全漏洞。本文将详细介绍 Trivy 的功能、工作原理以及如何使用 Trivy 保障镜像安全的实践案例。

***

![](image/image_a4-dZl-pIt.png)

# 1 概念

## 1.1 什么是 Trivy？

**Trivy** 是由 Aqua Security 开发的一款轻量级、易用的开源安全扫描工具，广泛用于扫描容器镜像、文件系统、Kubernetes 资源、IaC 文件等内容中的已知漏洞和错误配置。其主要目标是帮助开发者和安全团队在开发流程的早期发现潜在的安全问题，从而降低应用在生产环境中的安全风险。

**Trivy 支持的扫描对象包括**：

| **扫描对象**                                   | **说明**                                                                                                                     |
| ------------------------------------------ | -------------------------------------------------------------------------------------------------------------------------- |
| **容器镜像****（** ​**Container Image）**        | Trivy会扫描容器镜像（如 Docker 镜像）中的操作系统包和应用程序依赖，检测它们是否存在已知的安全漏洞。                                                                   |
| **文件系统（Filesystem）**                       | Trivy会扫描本地文件系统，检查文件中是否存在已知漏洞、敏感信息（如密码、密钥）或错误配置。                                                                            |
| **Git 代码仓库（Git Repository ）**              | Trivy会扫描Git 代码仓库（如 GitHub 或 GitLab），检测代码库中的代码、配置文件、依赖项是否存在潜在的安全问题。                                                         |
| **虚拟机镜像****（** ​**Virtual Machine Image）** | Trivy会扫描虚拟机镜像文件，如 `.qcow2`、`.vmdk` 等，帮助用户在虚拟化环境中检测安全漏洞和配置问题。                                                               |
| **Kubernetes**                             | Trivy会扫描 Kubernetes 资源（如 YAML 配置文件），检测其中的配置错误和安全问题，如 Pod 中的过度权限、未加密的通信通道等。                                                 |
| **AWS**                                    | Trivy会扫描 Amazon Web Services (AWS) 云环境，识别其中的安全问题和不安全配置。例如，检查 AWS 的 IAM 角色权限、S3 存储桶配置或安全组设置。                                |
| **基础设施即代码****工具****（IaC）**                 | Trivy支持扫描 Terraform 配置文件和Helm Charts，识别潜在的安全问题和配置错误。例如，错误的安全组规则、过于宽松的 IAM 权限、不加密的存储服务、不安全的 Pod 安全策略（PodSecurityPolicies）等。 |

**Trivy 能够发现和检测的主要问题包括：**

| **从哪发现问题**               | **说明**                                                                                                                                                       |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **系统的软件包和应用程序依赖 (SBOM)** | Trivy会扫描容器镜像、文件系统、操作系统中的**软件包**和**应用程序依赖**。它会生成一个 **SBOM（Software Bill of Materials，软件物料清单）**，列出操作系统使用的所有软件包和应用程序的依赖项。通过这种方式，开发人员可以清楚地知道他们的系统或应用程序中有哪些具体的组件。 |
| **已知的安全漏洞（CVEs）**        | Trivy 会根据软件物料清单中的信息，检测这些依赖项是否包含**已知的安全漏洞**，这些漏洞通常来自 **CVE（Common Vulnerabilities and Exposures，通用漏洞披露）** 的数据库，使用漏洞数据库来匹配这些 CVEs，从而发现系统或应用中存在的安全隐患。           |
| **不符合规范配置文件**            | Trivy会扫描**基础设施即代码（IaC）** 文件，检测其中的错误配置。这包括 Kubernetes、Terraform、Helm 等工具的配置文件，可能的错误包括不安全的权限设置、暴露的端口、没有启用的加密等等。                                                |
| **敏感信息**                 | Trivy 会扫描代码和配置文件中是否包含**敏感信息**，如**密钥**、**密码**、**令牌**等，避免未经授权的访问或数据泄露的风险。                                                                                      |
| **软件许可证**                | Trivy 会扫描使用的开源组件并检查它们的**软件许可证**。某些许可证可能有使用限制或法律义务，企业在发布产品时需要确保所有使用的开源软件许可证符合合规性要求。                                                                           |

## 1.2 Trivy 的工作原理

### 1.2.1 Trivy的组件

Trivy 的核心架构由多个关键组件构成，以下是 Trivy 单机版架构图示例：

![](image/image_fNdC9j5fe8.png)

### 1.2.2 组件介绍

- **扫描器（Vulnerability Scanner**）：这个组件专门负责漏洞扫描任务，利用从漏洞数据库获取的数据，匹配已知的安全漏洞。它与 **Vulnerability DB** 直接交互，确保扫描基于最新的安全信息。
- **配置扫描器（Config Scanner**）：专门用于扫描基础设施即代码（IaC）文件，检测其中的错误配置。它能够分析配置文件（如 Kubernetes YAML、Terraform 文件）是否符合安全最佳实践。
- **漏洞数据库（Vulnerability DB**）：Trivy 的漏洞数据库，会从多个漏洞源自动更新数据，用于扫描中匹配目标中的漏洞。数据库可以本地缓存，确保快速扫描。
- **缓存管理器（Cache Manager**）：Trivy 会将之前扫描结果和漏洞数据缓存，避免重复下载或计算，提升后续扫描速度。
- **报告生成器（Report Generator**）：负责生成扫描结果的报告，支持多种格式输出（如 JSON、表格），便于集成到 CI/CD 流程或其他分析工具中。

***

### **Trivy 的工作原理**

![](image/image_0f-ujIy4Jk.png)

&#x20;上图详细展示了 Trivy 客户端和服务器如何协同工作，扫描容器镜像的安全漏洞。分为以下几个步骤：

1. **下载漏洞数据库（Download vulnerability DB）** &#x20;

   Trivy 服务器首先从 GitHub 的漏洞数据库（`trivy-db`）中下载最新的漏洞信息。这个数据库包含 CVE（通用漏洞披露）等已知漏洞的信息。
2. **从容器注册表中拉取镜像层（Pull layers）** &#x20;

   Trivy 客户端开始扫描时，会从容器注册表（如 Docker Registry）中拉取目标镜像的层信息。每个容器镜像由多个层组成，客户端需要这些层来分析内容。
3. **分析拉取的镜像层（Analyze）** &#x20;

   客户端将从容器中拉取的镜像层进行分析，提取操作系统包、软件依赖、配置文件等信息，以查找潜在的漏洞。
4. **发送镜像层信息（Send layer information）** &#x20;

   分析完成后，Trivy 客户端将这些镜像层信息发送给 Trivy 服务器。客户端并不会独立查找漏洞，而是依赖服务器的漏洞数据库来完成匹配。
5. **Trivy 服务器存储缓存（Store cache）** &#x20;

   服务器会将收到的镜像层信息以及扫描结果存储在本地缓存中（如 Redis 或本地文件系统），以加速未来的扫描。如果相同镜像的某些层已经存在缓存中，则不必重新扫描。
6. **返回漏洞扫描结果（Respond vulnerabilities）** &#x20;

   最后，Trivy 服务器根据镜像层的信息，与漏洞数据库中的记录进行比对，并返回漏洞扫描结果。客户端接收这些信息并生成最终的报告。

> ❔ 说明： 如果是使用单机版，则扫描、分析和输出报告的工作都是本机完成。

***

# 2 小试牛刀：Trivy 实践案例

> ❔ 说明：本实验系统环境是Ubuntu22.04， trivy版本是0.55.1

让我们通过一些简单的示例，来了解如何使用 Trivy 在不同场景下进行扫描和查看其输出的信息。

## **安装 Trivy**

### 2.1.1 通过包管理工具安装

通过包管理工具安装，执行以下命令安装：

```text 
sudo apt-get install wget apt-transport-https gnupg
wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | gpg --dearmor | sudo tee /usr/share/keyrings/trivy.gpg > /dev/null
echo "deb [signed-by=/usr/share/keyrings/trivy.gpg] https://aquasecurity.github.io/trivy-repo/deb generic main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
sudo apt-get update
sudo apt-get install trivy
```


### 2.1.2 通过二进制方式安装

> ❔ 参考: [trivy二进制包下载地址](https://github.com/aquasecurity/trivy/releases "trivy二进制包下载地址")

下载二进制安装包

```bash 
mkdir trivy
cd trivy
wget https://github.com/aquasecurity/trivy/releases/download/v0.55.1/trivy_0.55.1_Linux-64bit.tar.gz
```


解压安装包

```bash 
TEMP_DIR=$(mktemp -d)   
tar xvf trivy_0.55.1_Linux-64bit.tar.gz -C ${TEMP_DIR}
sudo cp ${TEMP_DIR}/trivy /usr/local/bin/   
```


### 2.1.3 查看trivy版本

```bash 
root@ub22:~/trivy# trivy version
Version: 0.55.1
```


## 2.2 扫描容器镜像

下面以一个常见的 Docker 镜像 `nginx:1.26.1` 为例，运行 Trivy 扫描镜像中的已知漏洞：

执行下命令开始扫描：

```bash 
trivy image nginx:1.26.1
```


- 该命令会下载漏洞数据库并分析 `nginx` 镜像中的系统依赖和应用程序依赖。

完成扫描后，Trivy 会输出一份详细报告，列出镜像中包含的所有已知漏洞：

```bash 
root@jenkins:~# trivy image harbor.zx/hcie/nginx:1.26.1
# 下载并更新DB
2024-09-13T18:22:36+08:00       INFO    [db] Need to update DB
2024-09-13T18:22:36+08:00       INFO    [db] Downloading DB...  repository="ghcr.io/aquasecurity/trivy-db:2"
53.13 MiB / 53.13 MiB [-----------------------------------------] 100.00% 3.43 MiB p/s 16s
2024-09-13T18:22:54+08:00       INFO    [vuln] Vulnerability scanning is enabled
2024-09-13T18:22:54+08:00       INFO    [secret] Secret scanning is enabled
2024-09-13T18:22:54+08:00       INFO    [secret] If your scanning is slow, please try '--scanners vuln' to disable secret scanning
2024-09-13T18:22:54+08:00       INFO    [secret] Please see also https://aquasecurity.github.io/trivy/v0.55/docs/scanner/secret#recommendation for faster secret detection
2024-09-13T18:23:12+08:00       INFO    Java DB Repository      repository=ghcr.io/aquasecurity/trivy-java-db:1
2024-09-13T18:23:12+08:00       INFO    Downloading the Java DB...
643.22 MiB / 643.22 MiB [--------------------------------------] 100.00% 11.67 MiB p/s 55s
2024-09-13T18:24:10+08:00       INFO    The Java DB is cached for 3 days. If you want to update the database more frequently, "trivy clean --java-db" command clears the DB cache.
2024-09-13T18:24:10+08:00       INFO    Detected OS     family="debian" version="12.6"
2024-09-13T18:24:10+08:00       INFO    [debian] Detecting vulnerabilities...   os_version="12" pkg_num=149
2024-09-13T18:24:10+08:00       INFO    Number of language-specific files       num=0
2024-09-13T18:24:10+08:00       WARN    Using severities from other vendors for some vulnerabilities. Read https://aquasecurity.github.io/trivy/v0.55/docs/scanner/vulnerability#severity-selection for details.

harbor.zx/hcie/nginx:1.26.1 (debian 12.6)
# 扫描结果，容器镜像存在的漏洞数量
Total: 158 (UNKNOWN: 0, LOW: 88, MEDIUM: 48, HIGH: 16, CRITICAL: 6)
# 漏洞分析报告，内容比较长，不在这里
┌────────────────────┬─────────────────────┬──────────┬──────────────┬─────────────────────────┬───────────────────┬──────────────────────────────────────────────────────────────┐
│      Library       │    Vulnerability    │ Severity │    Status    │    Installed Version    │   Fixed Version   │                            Title                             │
├────────────────────┼─────────────────────┼──────────┼──────────────┼─────────────────────────┼───────────────────┼──────────────────────────────────────────────────────────────┤
│ apt                │ CVE-2011-3374       │ LOW      │ affected     │ 2.6.1                   │                   │ It was found that apt-key in apt, all versions, do not       │
│                    │                     │          │              │                         │                   │ correctly...                                                 │
│                    │                     │          │              │                         │                   │ https://avd.aquasec.com/nvd/cve-2011-3374                    │
├────────────────────┼─────────────────────┤          │              ├─────────────────────────┼───────────────────┼──────────────────────────────────────────────────────────────┤
...


```


如果想只看级别高的漏洞，可以执行以下命令：

```bash 
trivy image -s "HIGH,CRITICAL" harbor.zx/hcie/nginx:1.26.1

```


默认源是指向`ghcr.io/aquasecurity/trivy-db:2`， 所以下载和更新都比较慢，想要离线扫描，可以执行

```bash 
trivy image --skip-db-update --skip-java-db-update --offline-scan harbor.zx/hcie/nginx:1.26.1
```


如果对镜像包扫描，可以执行:

```bash 
trivy image --input ruby-3.1.tar

```


如果对输出格式有要求，可以执行：

```bash 
trivy image --format json --output result.json alpine:3.15

```


❔ 常用参数说明： &#x20;

| **参数**                      | **描述**      | **可能值**                                                                         |
| --------------------------- | ----------- | ------------------------------------------------------------------------------- |
| **-s, --severity**          | 指定安全级别      | UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL，默认是UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL            |
| **--input**                 | 指定镜像压缩包     | 绝对路径或相对目录                                                                       |
| **--format**                | 指定输出格式      | table,json,template,sarif,cyclonedx,spdx,spdx-json,github,cosign-vuln, 默认是table |
| **--output**                | 指定输出路径      | 绝对路径或相对目录                                                                       |
| **--scanners**              | 指定扫描器       | vuln,misconfig,secret,license，默认是vuln,secret                                    |
| **--offline-scan**          | 离线扫描漏洞      | N/A                                                                             |
| **--skip-db-update**        | 跳过更新CVE漏洞库  | N/A                                                                             |
| **--skip-java-db-update**   | 跳过更新java缺陷库 | N/A                                                                             |
| **--download-db-only**      | 只下载CVE漏洞库   | N/A                                                                             |
| **--download-java-db-only** | 只下载java缺陷库  | N/A                                                                             |

## 2.3 扫描项目文件

如果你有个python的项目，那么可以指定该项目的requirements.txt文件，或者如果如果你的项目使用 Pipfile.lock，也可以扫描这个文件：

以下测试的requirements.txt内容：

```bash 
Flask==1.1.2
requests==2.24.0
numpy==1.18.5

```


指定requirement.txt文件进行扫描

```bash 
trivy fs ~/flask-test/requirements.txt
```


或者指定Pipfile.lock文件进行扫描：

```bash 
trivy fs ~/flask-test/Pipfile.lock

```


输出结果如下：

```bash 
2024-09-13T21:57:35+08:00       INFO    [db] Need to update DB
2024-09-13T21:57:35+08:00       INFO    [db] Downloading DB...  repository="ghcr.io/aquasecurity/trivy-db:2"
53.11 MiB / 53.11 MiB [----------------------------------------] 100.00% 5.88 MiB p/s 9.2s
2024-09-13T21:57:59+08:00       INFO    [vuln] Vulnerability scanning is enabled
2024-09-13T21:57:59+08:00       INFO    [secret] Secret scanning is enabled
2024-09-13T21:57:59+08:00       INFO    [secret] If your scanning is slow, please try '--scanners vuln' to disable secret scanning
2024-09-13T21:57:59+08:00       INFO    [secret] Please see also https://aquasecurity.github.io/trivy/v0.55/docs/scanner/secret#recommendation for faster secret detection
2024-09-13T21:57:59+08:00       WARN    [pip] Unable to find python `site-packages` directory. License detection is skipped.      err="site-packages directory not found"
2024-09-13T21:57:59+08:00       INFO    Number of language-specific files       num=1
2024-09-13T21:57:59+08:00       INFO    [pip] Detecting vulnerabilities...

requirements.txt (pip)

Total: 7 (UNKNOWN: 0, LOW: 0, MEDIUM: 5, HIGH: 2, CRITICAL: 0)

```


## 2.4 扫描**Kubernetes**

除了镜像，Trivy 还可以扫描 Kubernetes 集群中的资源文件，例如 Pod 和 Deployment。通过以下命令，可以快速扫描 Kubernetes 的 YAML 文件：

该命令会扫描`kube-system`命名空间下的资源文件并输出可能存在的安全风险：

```bash 
trivy k8s --include-namespaces kube-system --report summary

```


检查结果输出如下：

```bash 
Summary Report for kube-admin@kubernetes


Workload Assessment
┌───────────┬──────────┬───────────────────┬───────────────────┬───────────────────┐
│ Namespace │ Resource │  Vulnerabilities  │ Misconfigurations │      Secrets      │
│           │          ├───┬───┬───┬───┬───┼───┬───┬───┬───┬───┼───┬───┬───┬───┬───┤
│           │          │ C │ H │ M │ L │ U │ C │ H │ M │ L │ U │ C │ H │ M │ L │ U │
└───────────┴──────────┴───┴───┴───┴───┴───┴───┴───┴───┴───┴───┴───┴───┴───┴───┴───┘
Severities: C=CRITICAL H=HIGH M=MEDIUM L=LOW U=UNKNOWN


Infra Assessment
┌─────────────┬──────────────────────────────────────────────┬─────────────────────┬──────────────────────┬───────────────────┐
│  Namespace  │                   Resource                   │   Vulnerabilities   │  Misconfigurations   │      Secrets      │
│             │                                              ├───┬────┬────┬───┬───┼───┬────┬────┬────┬───┼───┬───┬───┬───┬───┤
│             │                                              │ C │ H  │ M  │ L │ U │ C │ H  │ M  │ L  │ U │ C │ H │ M │ L │ U │
├─────────────┼──────────────────────────────────────────────┼───┼────┼────┼───┼───┼───┼────┼────┼────┼───┼───┼───┼───┼───┼───┤
│ kube-system │ ConfigMap/extension-apiserver-authentication │   │    │    │   │   │   │    │ 1  │    │   │   │   │   │   │   │
│ kube-system │ DaemonSet/calico-node                        │   │    │    │   │   │   │ 10 │ 14 │ 35 │   │   │   │   │   │   │
│ kube-system │ Service/coredns-coredns                      │   │    │    │   │   │   │    │ 1  │    │   │   │   │   │   │   │
│ kube-system │ Service/metrics-server                       │   │    │    │   │   │   │    │ 1  │    │   │   │   │   │   │   │
│ kube-system │ Deployment/calico-typha                      │   │    │    │   │   │   │ 2  │ 2  │ 9  │   │   │   │   │   │   │
│ kube-system │ Deployment/metrics-server                    │   │    │    │   │   │   │    │ 2  │ 4  │   │   │   │   │   │   │
│ kube-system │ Deployment/calico-kube-controllers           │   │    │    │   │   │   │ 1  │ 4  │ 9  │   │   │   │   │   │   │
│ kube-system │ Deployment/coredns-coredns                   │ 4 │ 58 │ 40 │ 2 │   │   │ 2  │ 4  │ 5  │   │   │   │   │   │   │
│ kube-system │ Service/calico-typha                         │   │    │    │   │   │   │    │ 1  │    │   │   │   │   │   │   │
└─────────────┴──────────────────────────────────────────────┴───┴────┴────┴───┴───┴───┴────┴────┴────┴───┴───┴───┴───┴───┴───┘
Severities: C=CRITICAL H=HIGH M=MEDIUM L=LOW U=UNKNOWN


RBAC Assessment
┌─────────────┬─────────────────────────────────────────────────────┬───────────────────┐
│  Namespace  │                      Resource                       │  RBAC Assessment  │
│             │                                                     ├───┬───┬───┬───┬───┤
│             │                                                     │ C │ H │ M │ L │ U │
├─────────────┼─────────────────────────────────────────────────────┼───┼───┼───┼───┼───┤
│ kube-system │ Role/system:controller:cloud-provider               │   │   │ 1 │   │   │
│ kube-system │ Role/system:controller:bootstrap-signer             │   │   │ 1 │   │   │
│ kube-system │ Role/system:controller:token-cleaner                │   │   │ 1 │   │   │
│ kube-system │ Role/system::leader-locking-kube-controller-manager │   │   │ 1 │   │   │
│ kube-system │ Role/system::leader-locking-kube-scheduler          │   │   │ 1 │   │   │
└─────────────┴─────────────────────────────────────────────────────┴───┴───┴───┴───┴───┘
Severities: C=CRITICAL H=HIGH M=MEDIUM L=LOW U=UNKNOWN

```


> ❔ 常用参数说明：

| **参数**                       | **描述**                                             | **可能值**                 |
| ---------------------------- | -------------------------------------------------- | ----------------------- |
| **--report**                 | 指定输出报告的格式                                          | all,summary， 默认值是all    |
| **--kubeconfig**             | 指定kubeconfig配置文件                                   | 默认是\~/.kube/config      |
| **--skip-images**            | 将阻止下载当前被扫描集群资源中的镜像（包括漏洞和机密），默认是下载全部当前扫描集群的所用镜像进行扫描 | N/A                     |
| **--include-kinds**          | 标志来控制将发现哪些类型的资源                                    | 与--exclude-kinds互斥      |
| **--exclude-kinds**          | 标志来控制将排除哪些类型的资源                                    | 与--include-kinds互斥      |
| **--include-namespaces**     | 标志来控制将发现哪些命名空间                                     | 与--exclude-namespaces互斥 |
| **--exclude-namespaces**     | 标志来控制将排除哪些命名空间                                     | 与--include-namespaces互斥 |
| **--disable-node-collector** | 标志只扫描控制节点                                          | N/A                     |

***

# 3 总结

通过本文， 你能了解Trivy是一个高效且灵活的安全扫描工具，覆盖从容器镜像的安全扫描，到 Kubernetes 资源等多方面的安全检测工作，帮助开发者和运维团队构建更加安全的云原生应用。借助 Trivy，您可以轻松提高容器镜像和云原生架构的安全性，助力业务的稳定发展。下一步，您可以在工作中使用 Trivy，并将其作为标准的安全工具之一。

***

# 4 参考资料

\[1] [Trivy 官方文档](https://aquasecurity.github.io/trivy/ "Trivy 官方文档")

\[2] [GitHub - Trivy](https://github.com/aquasecurity/trivy "GitHub - Trivy")
