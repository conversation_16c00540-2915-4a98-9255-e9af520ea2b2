# 一文学会Harbor的存储引擎-Docker Registery 2.0

## 目录

- [1. Harbor 镜像存储路径](#1-Harbor-镜像存储路径)
- [2. 文件系统结构](#2-文件系统结构)
  - [目录说明：](#目录说明)
- [3. 查看镜像层的步骤](#3-查看镜像层的步骤)
  - [3.1 查找镜像的 Digest](#31-查找镜像的-Digest)
  - [3.2 在文件系统中查找镜像层](#32-在文件系统中查找镜像层)
- [4. 工具辅助查看镜像层信息](#4-工具辅助查看镜像层信息)
  - [使用 crane 查看镜像层信息：](#使用-crane-查看镜像层信息)
  - [使用 skopeo 查看镜像层信息：](#使用-skopeo-查看镜像层信息)
- [总结](#总结)
- [Harbor 的存储结构](#Harbor-的存储结构)
- [Harbor 与文件系统存储](#Harbor-与文件系统存储)
- [为什么 AUFS 与 Harbor 无关？](#为什么-AUFS-与-Harbor-无关)
- [文件系统的选择与 Harbor](#文件系统的选择与-Harbor)
- [小结](#小结)

在 Harbor 中，镜像的层（Layer）是以分层结构存储的，尤其是在使用**文件系统作为存储后端**时，这些层会被存储在指定的目录结构中。你可以通过查看 Harbor 后端文件系统中的文件来查看这些镜像层。下面是详细步骤以及相关命令，帮助你了解如何在文件系统中找到并查看镜像层。

### 1. Harbor 镜像存储路径

当 Harbor 使用文件系统作为存储后端时，镜像的层数据会存储在 Harbor 的存储目录中，这个路径可以在 Harbor 的配置文件 `harbor.yml` 中找到。默认情况下，这个存储路径通常位于 `/data/registry`，但你可以根据实际配置进行查找。

```yaml
storage_service:
  filesystem:
    rootdirectory: /data/registry
```

镜像层的具体数据会存储在这个 `rootdirectory` 路径下，通常位于 `/data/registry/docker/registry/v2/blobs` 目录。

### 2. 文件系统结构

在文件系统中，Harbor 的镜像存储目录遵循以下结构：

```markdown
/data/registry/docker/registry/v2/
├── blobs
│   ├── sha256
│   │   ├── <layer 1>
│   │   ├── <layer 2>
│   │   └── ...
├── repositories
│   ├── <project>
│   │   ├── <repository>
│   │   │   └── _manifests
│   └── ...
```

#### 目录说明：

- **`blobs/sha256/`**：存放镜像的层（Layer），这些层是基于内容生成的哈希值（SHA256）进行组织的。
- **`repositories/`**：存放镜像的元数据，包括项目、仓库以及镜像的 manifest 文件（清单文件）。

### 3. 查看镜像层的步骤

#### 3.1 查找镜像的 Digest

首先，找到你要查看的镜像的 **Digest**，Digest 是镜像唯一标识符，可以通过 `docker` 或 `crane` 工具获取镜像的 Digest。

1. **列出镜像的 Digest**：

   你可以通过以下命令列出镜像的详细信息，包括它的 Digest：

   ```docker
   docker pull <registry>/<project>/<repository>:<tag>
   docker inspect <registry>/<project>/<repository>:<tag>
   ```

   输出中 `RepoDigests` 字段会显示镜像的 Digest，例如：

   ```json
   "RepoDigests": [
       "<registry>/<project>/<repository>@sha256:abc123def456..."
   ]
   ```

#### 3.2 在文件系统中查找镜像层

根据获取到的 Digest 值，你可以在 Harbor 的存储文件系统中找到对应的镜像层。

1. **进入 Harbor 的存储目录**：

   Harbor 使用的存储目录通常在 `/data/registry/docker/registry/v2/`。

   ```bash
   cd /data/registry/docker/registry/v2/blobs/sha256/
   ```
2. **查找镜像层**：

   镜像的层是根据 SHA256 哈希值存储的，层数据位于 `blobs/sha256/` 目录下。每个镜像层都是一个唯一的 SHA256 哈希值目录，内容会以 `data` 文件存储。

   ```bash
   ls /data/registry/docker/registry/v2/blobs/sha256/<digest_prefix>
   ```

   其中，`<digest_prefix>` 是镜像层的前两位 SHA256 哈希前缀。例如，如果镜像层的哈希值是 `sha256:abc123def456...`，那么对应的层数据会位于 `sha256/ab/` 目录下。
3. **查看镜像层的具体内容**：

   进入镜像层的目录，里面存放着 `data` 文件，文件即为该镜像层的实际数据。你可以通过命令查看具体的层文件：

   ```bash
   cd /data/registry/docker/registry/v2/blobs/sha256/<digest_prefix>/<digest>
   ls -l
   ```

   输出中你会看到类似这样的文件：

   ```markdown
   -rw-r--r-- 1 <USER> <GROUP> 12345678 date data
   ```

   文件名为 `data`，这是镜像层的二进制数据，可以通过其他工具进行进一步的分析，但通常我们不直接查看层的内容，而是通过工具管理镜像层。

### 4. 工具辅助查看镜像层信息

除了直接查看文件系统，使用工具如 `crane` 或 `skopeo` 等可以帮助你快速查看镜像的分层信息。

#### 使用 `crane` 查看镜像层信息：

`crane` 是一个用于管理容器镜像的 CLI 工具。你可以使用 `crane` 来列出镜像的层。

1. 安装 `crane`：

   ```go
   go install github.com/google/go-containerregistry/cmd/crane@latest
   ```
2. 列出镜像的层：

   ```bash
   crane manifest <registry>/<project>/<repository>:<tag>
   ```

   该命令会输出镜像的 manifest（清单），其中包含每个镜像层的哈希值。例如：

   ```json
   {
     "schemaVersion": 2,
     "config": {
       "mediaType": "application/vnd.docker.container.image.v1+json",
       "digest": "sha256:abc123def456..."
     },
     "layers": [
       {
         "mediaType": "application/vnd.docker.image.rootfs.diff.tar.gzip",
         "size": 12345678,
         "digest": "sha256:xyz987654..."
       },
       ...
     ]
   }
   ```

#### 使用 `skopeo` 查看镜像层信息：

`skopeo` 是另一个强大的容器镜像管理工具，支持查看镜像的层和其他元数据。

1. 安装 `skopeo`：

   ```bash
   sudo apt-get install skopeo
   ```
2. 查看镜像的层信息：

   ```bash
   skopeo inspect docker://<registry>/<project>/<repository>:<tag>
   ```

   该命令将输出与镜像相关的详细信息，包括镜像的层哈希值。

### 总结

在 Harbor 使用文件系统存储时，镜像层以分层存储的方式存在于 `blobs` 目录下。你可以通过镜像的 Digest 查找到对应的层，并在文件系统中查看其物理存储位置。虽然可以直接在文件系统中访问这些层的二进制文件，通常我们会借助工具如 `docker inspect`、`crane` 或 `skopeo` 来获取镜像层的详细信息，以便更直观地查看层的结构。

这些工具可以帮助我们更好地理解镜像的分层，并验证镜像的完整性和内容。

在 **Harbor** 中，文件系统的存储后端本质上并不依赖于 **AUFS** 或其他具体的文件系统类型。**Harbor** 作为容器镜像仓库，使用的是 **OCI 镜像规范**，存储镜像的方式与容器运行时（如 Docker）有所不同。Harbor 将镜像数据存储在其指定的存储后端，而不涉及具体的文件系统驱动如 AUFS、OverlayFS 等。

### Harbor 的存储结构

Harbor 使用**分层存储**模型，通过 **Docker Registry**（即 OCI 兼容的容器镜像注册表）的 **v2 API** 来存储和管理容器镜像。镜像数据被组织为多个层（Layer），这些层按照内容生成的 **SHA256 哈希值**存储在 Harbor 的后端存储系统中，具体可以是本地文件系统、NFS、Ceph、S3 或其他对象存储服务。每一层对应一个只读的内容块，这些块并不会被重复存储。

### Harbor 与文件系统存储

如果 Harbor 的存储后端配置为文件系统（通常是本地的 ext4、xfs 等常见文件系统），这些镜像层会存储在指定的文件系统路径下（例如 `/data/registry/docker/registry/v2`）。这个目录结构下的镜像层以文件的形式存在，并由文件系统本身管理读写操作。具体文件存储位置取决于镜像的哈希值，存储在 `blobs/sha256/` 目录下。

因此，Harbor 并不直接使用容器运行时中的文件系统驱动，如 AUFS、OverlayFS 等。AUFS 是 Docker 容器在运行时使用的一种 **联合文件系统**，用于管理容器的层次结构和可写层。当你启动一个容器时，Docker 使用类似 AUFS、OverlayFS 或其他文件系统驱动来构建和管理容器的文件系统，允许容器共享镜像层，同时提供一个可写的层来存储运行时产生的变化。

### 为什么 AUFS 与 Harbor 无关？

- **Harbor 是镜像仓库**：Harbor 负责存储和管理镜像的各个层，而不直接参与容器的运行时管理。存储的镜像层是只读的，并不会像 AUFS 在容器运行时那样提供一个可写层。
- **Docker 运行时的文件系统驱动**：当你在 Docker 中运行容器时，Docker 通过 AUFS、OverlayFS 或其他驱动来管理容器的文件系统，确保镜像层的共享和数据隔离。这与 Harbor 提供的镜像存储服务是两个不同的功能层面。

### 文件系统的选择与 Harbor

在 **Harbor** 配置文件 `harbor.yml` 中，文件系统是作为一种存储后端选项存在的。例如：

```yaml
storage_service:
  filesystem:
    rootdirectory: /data/registry
```

这种文件系统存储后端与 AUFS 无关，它依赖的是你系统上使用的文件系统类型，如 ext4、xfs 或 NFS。Harbor 只负责将镜像层按内容存储在指定的目录路径下，而具体的存储逻辑由操作系统的文件系统管理。

### 小结

Harbor 本身并不使用 AUFS，AUFS 是 Docker 等容器引擎在运行时使用的一种联合文件系统驱动。Harbor 负责存储和管理镜像数据，而镜像数据的存储与访问通过指定的存储后端（如文件系统、NFS、S3 等）完成。Harbor 的文件系统存储并不与 AUFS 或其他运行时文件系统驱动挂钩，它使用的文件系统取决于操作系统的配置和选择。
